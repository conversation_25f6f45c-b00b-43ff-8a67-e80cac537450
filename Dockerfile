FROM node as builder

# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
COPY package*.json ./


RUN npm ci

COPY . .

RUN npm run build

FROM node:slim

ENV NODE_ENV production
USER node

# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
COPY package*.json ./

RUN npm ci --production

COPY --from=builder /usr/src/app/dist ./dist
RUN if [ -d "/usr/src/app/uploads" ]; then cp -r /usr/src/app/uploads ./uploads; else mkdir -p ./uploads; fi
COPY --from=builder /usr/src/app/src/domain/email_templates/. ./dist/domain/email_templates/.
CMD [ "node", "dist/main.js" ]