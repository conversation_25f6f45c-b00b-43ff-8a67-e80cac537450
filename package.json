{"name": "pig-farm-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli.js -d src/infrastructure/config/typeorm/typeorm.module.ts", "migration:generate": "npm run build && npm run typeorm -- migration:generate ./src/migrations/$npm_config_name", "migration:run": "npm run typeorm -- migration:run", "migration:create": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli migration:create ./src/migrations/$npm_config_name", "migration:revert": "npm run typeorm -- migration:revert"}, "dependencies": {"@golevelup/nestjs-rabbitmq": "^4.0.0", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/cache-manager": "^2.2.2", "@nestjs/class-transformer": "^0.4.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.2.2", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.3.8", "@nestjs/swagger": "^7.1.10", "@nestjs/terminus": "^10.0.1", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.3.8", "@opentelemetry/api": "^1.6.0", "@socket.io/redis-adapter": "^8.3.0", "@types/multer": "^1.4.11", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.3", "axios": "^1.5.0", "axios-retry": "^3.7.0", "bcryptjs": "^2.4.3", "cache-manager": "^5.5.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "dayjs": "^1.11.10", "ejs": "^3.1.10", "elastic-apm-node": "^4.0.0", "exceljs": "^4.4.0", "graph-data-structure": "^3.5.0", "ioredis": "^5.4.1", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "^8.9.1", "nodemailer": "^6.9.13", "passport": "^0.7.0", "passport-azure-ad": "^4.3.5", "passport-jwt": "^4.0.1", "passport-microsoft": "^2.1.0", "pg": "^8.11.3", "pluralize": "^8.0.0", "redis": "^4.6.13", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.5", "typeorm": "^0.3.17"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}