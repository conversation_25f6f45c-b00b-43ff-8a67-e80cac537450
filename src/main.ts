import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { RedisIoAdapter } from './infrastructure/config/redis/redis-adpater';
import { HttpErrorFilter } from './utils/exception/http-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  // const redisIoAdapter = new RedisIoAdapter();

  // app.connectMicroservice<MicroserviceOptions>({
  //   transport: Transport.REDIS,
  //   options: redisIoAdapter.redisOptions(),
  // });

  app.setGlobalPrefix('auth-service');
  app.enableCors({
    origin: true,
    credentials: true,
  });
  const config = new DocumentBuilder()
    .setTitle('Identity API')
    .setDescription('The API description')
    .setVersion('1.0')
    .setBasePath('auth-service')
    .addBearerAuth(
      {
        description: ``,
        name: 'Authorization',
        bearerFormat: 'Bearer',
        scheme: 'Bearer',
        type: 'http',
        in: 'Header',
      },
      'Authorization',
    )
    .addBasicAuth(
      {
        description: 'Enter Basic Auth credentials',
        name: 'Authorization',
        scheme: 'basic',
        type: 'http',
        in: 'header',
      },
      'BasicAuth',
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('auth-service-doc/swagger', app, document, {
    swaggerOptions: {
      operationsSorter: 'method',
    },
  });
  //const rabbitConfigService = app.get(RabbitConfigService);
  app.useGlobalFilters(new HttpErrorFilter());
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));

  const redisIoAdapter = new RedisIoAdapter(app);
  await redisIoAdapter.connectToRedis();
  app.useWebSocketAdapter(redisIoAdapter);

  await app.listen(process.env.PORT || 8000);
}
bootstrap();
