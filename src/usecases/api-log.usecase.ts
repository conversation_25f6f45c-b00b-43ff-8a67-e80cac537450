import { Inject, Injectable } from '@nestjs/common';
import { ResponseDto } from '../domain/dtos/response.dto';
import { IApiLogRepository } from '../domain/repositories/api-log.repository';
import { GetListApiLogDto } from '../controller/api-log/dtos/get-list-api-log.dto';
import { ApiLogModel } from '../domain/model/api-log.model';
import { CreateApiLogDto } from '../controller/api-log/dtos/create-api-log.dto';

@Injectable()
export class ApiLogUsecases {
  constructor(
    @Inject(IApiLogRepository)
    private readonly apiLogRepository: IApiLogRepository,
  ) {}

  async getApiLogs(
    conditions: GetListApiLogDto,
  ): Promise<ResponseDto<ApiLogModel>> {
    return await this.apiLogRepository.getList(conditions);
  }

  async create(data: CreateApiLogDto): Promise<ApiLogModel> {
    return await this.apiLogRepository.create(data);
  }
}
