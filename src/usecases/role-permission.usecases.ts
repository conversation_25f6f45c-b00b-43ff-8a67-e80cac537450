import { Inject, Injectable } from '@nestjs/common';
import { CreateResourceRolePermissionDto } from '../controller/role/dtos/create-resource-role-permission.dto';
import { RolePermissionModel } from '../domain/model/role-permission.model';
import { IResourceRepository } from '../domain/repositories/resource.repository';
import { IRolePermissionRepository } from '../domain/repositories/role-permission.repository';

@Injectable()
export class RolePermissionUsecases {
  constructor(
    @Inject(IRolePermissionRepository)
    private readonly rolePermissionRepository: IRolePermissionRepository,
    @Inject(IResourceRepository)
    private readonly resourceRepository: IResourceRepository,
  ) {}
  async createRolePermissionFromResources(
    roleId: string,
    resourcePermissions: CreateResourceRolePermissionDto[],
  ): Promise<RolePermissionModel[]> {
    const rolePermissions: RolePermissionModel[] = [];

    for (const resourcePermission of resourcePermissions) {
      const {
        resourceId,
        resourceActionId,
        actionView,
        actionCreate,
        actionEdit,
        actionDelete,
        actions,
      } = resourcePermission;

      await this.resourceRepository.getResourceById(resourceId);

      if (actions && actions.length) {
        actions.map((action) => {
          const rolePermission = new RolePermissionModel({
            roleId,
            resourceId: action.resourceId,
            resourceActionId: action.resourceActionId,
            actionView: action.actionFull,
            actionCreate: action.actionFull,
            actionEdit: action.actionFull,
            actionDelete: action.actionFull,
          });
          rolePermissions.push(rolePermission);
        });
      }

      rolePermissions.push(
        new RolePermissionModel({
          roleId,
          resourceId,
          resourceActionId,
          actionView,
          actionCreate,
          actionEdit,
          actionDelete,
        }),
      );
    }

    return await this.rolePermissionRepository.createRolePermissions(
      rolePermissions,
    );
  }

  async deleteRolePermissionByRoleId(roleId: string): Promise<void> {
    await this.rolePermissionRepository.deleteRolePermissionByRoleId(roleId);
  }
}
