import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { readFileSync } from 'fs';
import { resolve } from 'path';
import { CreateUserDto } from '../controller/user/dtos/create-user.dto';
import { GetUserListDto } from '../controller/user/dtos/get-user-list.dto';
import { UpdateUserDto } from '../controller/user/dtos/update-user.dto';
import { userErrorDetails } from '../domain/messages/error-detail/user';
import { RoleModel } from '../domain/model/role.model';
import { UserModel } from '../domain/model/user.model';
import { IUserRepository } from '../domain/repositories/user.repository';
import { generatePassword } from '../utils/common';
import { EmailUsecases } from './email.usecases';
import { RoleUsecases } from './role.usecases';
import { AccountUsecases } from './account.usecase';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { IUserVerifyRepository } from '../domain/repositories/userVerifyRepository.interface';
import { ETokenType } from '../domain/config/enums/token-type.enum';
import { CreateUserVerifyDto } from '../controller/auth/dtos/create-user-verify.dto';
import { UserVerifyModel } from '../domain/model/user-verify.model';
import { UpdateUserVerifyDto } from '../controller/auth/dtos/update-user-verify.dto';
import { authErrorDetails } from '../domain/messages/error-detail/auth';

@Injectable()
export class UserVerifyUsecases {
  constructor(
    @Inject(IUserVerifyRepository)
    private readonly userRepository: IUserVerifyRepository,
  ) {}

  async getUserVerify(
    userId: string,
    tokenType: ETokenType,
  ): Promise<UserVerifyModel> {
    return await this.userRepository.getUserVerify(userId, tokenType);
  }

  async createUserVerify(data: CreateUserVerifyDto) {
    const userVerifyModel = new UserVerifyModel(data);

    await this.userRepository.createUserVerify(userVerifyModel);
  }

  async updateUserVerify(data: UpdateUserVerifyDto) {
    await this.userRepository.updateUserVerify(data.userId, data);
  }

  async deleteUserVerify(userId: string, tokenType: ETokenType) {
    await this.userRepository.deleteUserVerify(userId, tokenType);
  }
}
