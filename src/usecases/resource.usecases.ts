import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import Graph from 'graph-data-structure';
import { CreateResourceDto } from '../controller/resource/dtos/create-resource.dto';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { EPortal } from '../domain/config/enums/portal.enum';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { IResourceListByPlatform } from '../domain/interface/resource-list-by-platform.interface';
import { ResourceModel } from '../domain/model/resource.model';
import { IResourceRepository } from '../domain/repositories/resource.repository';
import { merge } from '../utils/common';

@Injectable()
export class ResourceUsecases {
  constructor(
    @Inject(IResourceRepository)
    private readonly resourceRepository: IResourceRepository,
  ) {}
  async createResource(data: CreateResourceDto): Promise<ResourceModel> {
    // if (!data.resourceOrder) {
    //   const maxResourceOrder =
    //     await this.resourceRepository.getMaxResourceOrder(data.parentId);

    //   data.resourceOrder = maxResourceOrder + 1;
    // }
    const resourceModel = new ResourceModel({
      resourceAliases: data.resourceAliases,
      description: data.description,
      parentId: data.parentId || null,
      resourceOrder: data.resourceOrder,
      platform: data.platform,
    });

    const resource =
      await this.resourceRepository.createResource(resourceModel);

    return resource;
  }

  async getResourceById(id: string): Promise<ResourceModel> {
    const resource = await this.resourceRepository.getResourceById(id);

    if (!resource) {
      throw new HttpException(
        { message: 'RESOURCE_NOT_FOUND' },
        HttpStatus.BAD_REQUEST,
      );
    }

    return resource;
  }

  async getResources(
    jwtPayload: IAuthUserPayload,
  ): Promise<IResourceListByPlatform> {
    let resources: ResourceModel[] = [];
    //Get resources by platform in token
    switch (jwtPayload?.platform) {
      case EPlatform.DIGI_AQUA: {
        resources = await this.resourceRepository.getResources({
          portals: [EPortal.WEB_ADMIN, EPortal.WEB_INTERNAL_PORTAL],
          platform: EPlatform.DIGI_AQUA,
        });

        break;
      }
      case EPlatform.DIGI_SALE: {
        resources = await this.resourceRepository.getResources({
          portals: [EPortal.WEB_ADMIN, EPortal.WEB_INTERNAL_PORTAL],
          platform: EPlatform.DIGI_SALE,
        });

        break;
      }
      case EPlatform.E_PURCHASE: {
        resources = await this.resourceRepository.getResources({
          portals: [EPortal.WEB_ADMIN, EPortal.WEB_INTERNAL_PORTAL],
          platform: EPlatform.E_PURCHASE,
        });

        break;
      }
    }

    const webAdmin = [];
    const webInternalPortal = [];

    if (resources && resources.length) {
      resources.forEach((resource) => {
        switch (resource.portal) {
          case EPortal.WEB_ADMIN: {
            webAdmin.push(resource);
            break;
          }
          case EPortal.WEB_INTERNAL_PORTAL: {
            webInternalPortal.push(resource);
            break;
          }
        }
      });
    }

    return { webAdmin, webInternalPortal };
  }

  async deleteResource(id: string): Promise<void> {
    await this.resourceRepository.deleteResource(id);
  }

  async getResourcesWithRolePermission(
    roleId: string,
    platform: EPlatform,
    portal: EPortal,
  ) {
    const rsp = await this.resourceRepository.getResourceByRoleId(
      roleId,
      platform,
      portal,
    );

    const resources = {};
    const resourceActions = {};

    rsp.forEach((resource) => {
      if (resource.roleId != roleId) {
        // continue
        return;
      }

      const prefix = resource.resourceId;

      if (!resourceActions[prefix]) {
        resourceActions[prefix] = [];
      }

      if (!resource.parentId) {
        resourceActions[prefix].push(resource.resourceAliases);
        resources[prefix] = {
          id: resource.resourceId,
          resourceAliases: resource.resourceAliases,
          resourceOrder: resource.resourceOrder,
          isEnabled: resource.isEnabled,
          parentId: resource.parentId,
          platform: resource.platform,
          portal: resource.portal,
          rolePermissions: resourceActions[prefix],
        };
      } else {
        // Khi sub actions loop trước module thì phải khai báo trước
        if (!resourceActions[resource.parentId]) {
          resourceActions[resource.parentId] = [];
        }
        //mapping resource actions
        resourceActions[resource.parentId].push(resource.resourceAliases);

        if (!resources[resource.parentId]) {
          resources[resource.parentId] = {};
        }
        resources[resource.parentId].rolePermissions =
          resourceActions[resource.parentId];
      }
    });

    for (const key of Object.keys(resources)) {
      resources[key].rolePermissions = Object.values(
        this.resourceInherit(rsp, resources[key]),
      );
    }

    return Object.values(resources);
  }

  private resourceInherit(resources, resourceCheck) {
    const graph = Graph();

    let firstDepth = -1;
    let currentDepth = -1;
    const nodesMapDepth = {};
    const nodesProperties = {};
    const nodesMapInherit = {};

    resources.forEach((resource) => {
      if (firstDepth == -1) firstDepth = resource.nlevel;
      const prefix = `${resource.parentId ?? resource.resourceId}_${
        resource.resourceAliases
      }`;

      currentDepth = resource.nlevel;

      graph.addNode(currentDepth + '_' + prefix);

      nodesProperties[currentDepth + '_' + prefix] = resource;
      nodesMapInherit[currentDepth] = resource.allowInherit;

      if (nodesMapDepth[prefix] && nodesMapDepth[prefix] != currentDepth) {
        graph.addEdge(
          nodesMapDepth[prefix] + '_' + prefix,
          currentDepth + '_' + prefix,
        );
      }

      nodesMapDepth[prefix] = currentDepth;
    });

    let rs = {};

    resourceCheck.rolePermissions.forEach((resAction) => {
      const prefix = `${resourceCheck.id}${resAction ? '_' + resAction : ''}`;

      const rsSearch = graph.depthFirstSearch([`${firstDepth}_${prefix}`]);

      if (nodesProperties[rsSearch[0]]) {
        const listInherit = this.fetchPermissionInherit(
          rsSearch,
          nodesProperties,
        );

        rs = merge(rs, listInherit);
      }
    });

    return rs;
  }

  private parseView(allowInherit, inherit, action) {
    const actionVal =
      (inherit && action) == false
        ? allowInherit && !inherit
          ? null
          : !allowInherit
            ? action
            : false
        : inherit && action;

    return !allowInherit && actionVal == null ? false : actionVal;
  }

  private permissionFormat(permission) {
    return {
      id: permission.id,
      actionView: permission.actionView,
      actionCreate: permission.actionCreate,
      actionEdit: permission.actionEdit,
      actionDelete: permission.actionDelete,
      roleId: permission.roleId,
      resourceId: permission.resourceId,
      resourceAliases: permission.resourceAliases,
      resourceActionId: permission.resourceActionId,
      allowInherit: permission.allowInherit,
      parentId: permission.parentId,
      platform: permission.platform,
      portal: permission.portal,
    };
  }

  private fetchPermissionInherit(permissionList, nodesProps) {
    const mergePermissionsInherit = {};
    const flagFirstPermissionsInherit = {};

    permissionList.reverse().forEach((permission) => {
      const ob = nodesProps[permission];

      const prefix = `${ob.id}${ob.parentId ? '_' + ob.resourceAliases : ''}`;
      if (!flagFirstPermissionsInherit[prefix]) {
        flagFirstPermissionsInherit[prefix] = ob;
      }

      ob.actionView = ob.actionView ?? false;
      ob.actionCreate = ob.actionCreate ?? false;
      ob.actionEdit = ob.actionEdit ?? false;
      ob.actionDelete = ob.actionDelete ?? false;

      mergePermissionsInherit[prefix] = {
        actionView: this.parseView(
          ob.allowInherit,
          mergePermissionsInherit[prefix]?.actionView,
          ob.actionView,
        ),
        actionCreate: this.parseView(
          ob.allowInherit,
          mergePermissionsInherit[prefix]?.actionCreate,
          ob.actionCreate,
        ),
        actionEdit: this.parseView(
          ob.allowInherit,
          mergePermissionsInherit[prefix]?.actionEdit,
          ob.actionEdit,
        ),
        actionDelete: this.parseView(
          ob.allowInherit,
          mergePermissionsInherit[prefix]?.actionDelete,
          ob.actionDelete,
        ),
      };

      mergePermissionsInherit[prefix] = this.permissionFormat(
        merge(
          flagFirstPermissionsInherit[prefix],
          mergePermissionsInherit[prefix],
        ),
      );

      if (!ob.allowInherit) {
        return;
      }
    });

    return mergePermissionsInherit;
  }
}
