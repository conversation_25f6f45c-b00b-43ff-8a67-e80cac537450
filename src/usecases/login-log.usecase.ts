import { Inject, Injectable } from '@nestjs/common';

import { ResponseDto } from '../domain/dtos/response.dto';

import { GetListLoginLogDto } from '../controller/auth/dtos/get-list-login-log.dto';
import { LoginLogDto } from '../controller/auth/dtos/login-log.dto';
import { LoginLogModel } from '../domain/model/login-log.model';
import { ILoginLogRepository } from '../domain/repositories/login-log.repository';

@Injectable()
export class LoginLogUsecases {
  constructor(
    @Inject(ILoginLogRepository)
    private readonly loginLogRepository: ILoginLogRepository,
  ) {}

  async getLoginLogs(
    conditions: GetListLoginLogDto,
    jwtPayload: any,
  ): Promise<ResponseDto<LoginLogModel>> {
    conditions.portals = [jwtPayload.portal];
    conditions.platform = jwtPayload.platform;
    return await this.loginLogRepository.getList(conditions);
  }

  async create(data: LoginLogDto): Promise<LoginLogModel> {
    return await this.loginLogRepository.create(data);
  }
}
