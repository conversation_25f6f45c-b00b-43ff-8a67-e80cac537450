import { Inject, Injectable } from '@nestjs/common';
import { CreateAccountDto } from '../controller/account/dtos/create-account.dto';
import { UpdateAccountDto } from '../controller/account/dtos/update-account.dto';
import { CreateUserDto } from '../controller/user/dtos/create-user.dto';
import { UpdateUserDto } from '../controller/user/dtos/update-user.dto';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { EStatus } from '../domain/config/enums/status.enum';
import { AccountModel } from '../domain/model/account.model';
import { IAccountRepository } from '../domain/repositories/account.repository';
import { CreateUserMobileDto } from '../controller/user/dtos/create-user-mobile.dto';

@Injectable()
export class AccountUsecases {
  constructor(
    @Inject(IAccountRepository)
    private readonly accountRepository: IAccountRepository,
  ) {}
  async createAccount(
    data: CreateAccountDto,
    jwtPayload: any,
  ): Promise<AccountModel> {
    const accountModel = new AccountModel({
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      staffId: data.staffId,
      staffCode: data.staffCode,
      platform: data.platform,
      createdBy: {
        id: jwtPayload?.userId,
        firstName: jwtPayload?.firstName,
        lastName: jwtPayload?.lastName,
        email: jwtPayload?.email,
        phone: jwtPayload?.phone,
        staffId: jwtPayload?.staffId,
        staffCode: jwtPayload?.staffCode,
      },
      status: data.status,
      isAdmin: data.isAdmin || false,
      userName: data.userName,
    });

    return await this.accountRepository.createAccount(accountModel);
  }

  async createMultiAccount(
    createAccountDtos: CreateAccountDto[],
    data: CreateUserDto | UpdateUserDto,
    userInfo: any,
  ): Promise<AccountModel[]> {
    const accounts: AccountModel[] = [];

    if (createAccountDtos && createAccountDtos.length) {
      for (const createAccountDto of createAccountDtos) {
        const account = await this.createAccount(
          { ...createAccountDto, userName: data.userName },
          userInfo,
        );
        accounts.push(account);
      }
    } else {
      const defaultAccount: CreateAccountDto = {
        firstName: null,
        lastName: null,
        email: null,
        staffId: null,
        staffCode: null,
        platform: userInfo?.platform || EPlatform.E_PURCHASE,
        status: EStatus.ACTIVE,
        isAdmin: false,
        userName: data.userName,
      };
      const account = await this.createAccount(defaultAccount, userInfo);
      accounts.push(account);
    }

    return accounts;
  }

  async deleteAccountByUserId(userId: string, jwtPayload: any): Promise<void> {
    await this.updateAccount(
      null,
      userId,
      {
        deletedBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
      },
      jwtPayload?.platform,
    );
    await this.accountRepository.deleteAccountByUserId(
      userId,
      jwtPayload.platform,
    );
  }

  async deleteAccountByStaffId(
    staffId: string,
    jwtPayload: any,
  ): Promise<void> {
    await this.updateAccount(
      staffId,
      null,
      {
        deletedBy: {
          id: jwtPayload?.userId,
          firstName: jwtPayload?.firstName,
          lastName: jwtPayload?.lastName,
          email: jwtPayload?.email,
          phone: jwtPayload?.phone,
          staffId: jwtPayload?.staffId,
          staffCode: jwtPayload?.staffCode,
        },
      },
      jwtPayload?.platform,
    );
    await this.accountRepository.deleteAccountByStaffId(staffId, null);
  }

  async getAccountsByStaffId(
    staffId: string,
    userId: string,
    platform: EPlatform = EPlatform.E_PURCHASE,
  ): Promise<AccountModel[]> {
    return await this.accountRepository.getAccountsByStaffId({
      staffId: staffId,
      userId,
      platform,
    });
  }

  async updateAccount(
    staffId: string,
    userId: string,
    updateAccountDto: UpdateAccountDto,
    platform: EPlatform = EPlatform.E_PURCHASE,
  ): Promise<void> {
    const accounts = await this.getAccountsByStaffId(staffId, userId, platform);
    if (accounts && accounts.length) {
      for (let account of accounts) {
        account = Object.assign(account, updateAccountDto);

        await this.accountRepository.updateAccount(account);
      }
    }
  }

  async getAccountByUserName(
    userName: string,
    platform: EPlatform = EPlatform.E_PURCHASE,
  ): Promise<AccountModel> {
    return await this.accountRepository.getAccountByUserName(
      userName,
      platform,
    );
  }

  async createMultiAccountMobile(
    createAccountDtos: CreateAccountDto[],
    data: CreateUserMobileDto,
    userInfo: any,
  ): Promise<AccountModel[]> {
    const accounts: AccountModel[] = [];

    if (createAccountDtos && createAccountDtos.length) {
      for (const createAccountDto of createAccountDtos) {
        const account = await this.createAccount(
          { ...createAccountDto, userName: data.userName },
          userInfo,
        );
        accounts.push(account);
      }
    } else {
      const defaultAccount: CreateAccountDto = {
        firstName: null,
        lastName: null,
        email: null,
        staffId: null,
        staffCode: null,
        platform: data.platform || EPlatform.E_PURCHASE,
        status: EStatus.ACTIVE,
        isAdmin: false,
        userName: data.userName,
      };
      const account = await this.createAccount(defaultAccount, userInfo);
      accounts.push(account);
    }

    return accounts;
  }
}
