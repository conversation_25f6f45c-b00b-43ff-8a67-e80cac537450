import { Injectable } from '@nestjs/common';
import * as FormData from 'form-data';
import { sendPost } from '../utils/http';
import * as stream from 'stream';
import { FileServiceApiUrlsConst } from '../utils/contants/file-service-api-url.constants';

@Injectable()
export class FileUsecases {
  constructor() {}

  async uploadFile(file: Express.Multer.File, data: any, path?: string) {
    console.log(11, file);
    let formData = new FormData();
    formData.append('file', file.buffer, file.originalname);
    console.log(14, file);
    if (path) {
      formData.append('path', path);
    }
    console.log(17, file);
    console.log(19, data);
    if (data) {
      console.log(20, data);
      for (const key in data) {
        console.log(19, file);
        formData.append(key, data[key]);
      }
    }
    console.log(27, '===== Uploading =====');
    console.log(28, FileServiceApiUrlsConst.UPLOAD_FILE());
    const uploadFile = await sendPost(
      FileServiceApiUrlsConst.UPLOAD_FILE(),
      formData,
      {
        ...formData.getHeaders(),
      },
    );
    console.log(29, '===== Uploaded =====');
    console.log(30, uploadFile?.data?.['data']);
    return uploadFile?.data?.['data'];
  }

  async uploadFiles(files: Express.Multer.File[], data: any, path?: string) {
    let formData = new FormData();

    files.forEach((file, index) =>
      formData.append(`files`, file.buffer, file.originalname),
    );

    if (path) {
      formData.append('path', path);
    }

    if (data) {
      console.log(20, data);
      for (const key in data) {
        formData.append(key, data[key]);
      }
    }

    const uploadFiles = await sendPost(
      FileServiceApiUrlsConst.UPLOAD_FILES(),
      formData,
      {
        ...formData.getHeaders(),
      },
    );

    return uploadFiles?.data?.['data'] || [];
  }

  async deleteFile(path: string) {
    await sendPost(FileServiceApiUrlsConst.DELETE_FILE(), {
      path,
    });
  }

  async deleteFiles(paths: string[]) {
    await sendPost(FileServiceApiUrlsConst.DELETE_FILES(), {
      paths,
    });
  }

  async bufferToMulterFile(
    buffer: Buffer,
    filename: string,
  ): Promise<Express.Multer.File> {
    const readable = new stream.PassThrough();
    readable.end(buffer);

    const multerFile: Express.Multer.File = {
      fieldname: 'file',
      originalname: filename,
      encoding: '7bit',
      mimetype:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      buffer,
      size: buffer.length,
      stream: readable as any,
      destination: '',
      filename,
      path: '',
    };

    return multerFile;
  }
}
