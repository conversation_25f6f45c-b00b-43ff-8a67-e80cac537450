import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { CreateDataPermissionDto } from '../controller/role/dtos/create-data-permission.dto';
import { CreateRoleDto } from '../controller/role/dtos/create-role.dto';
import { GetRoleListDto } from '../controller/role/dtos/get-role-list.dto';
import { UpdateRoleDto } from '../controller/role/dtos/update-role.dto';
import { DataRoleType } from '../domain/config/enums/data-role-type.enum';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { EPortal } from '../domain/config/enums/portal.enum';
import { EStatus } from '../domain/config/enums/status.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { roleErrorDetails } from '../domain/messages/error-detail/role';
import { RoleModel } from '../domain/model/role.model';
import { IRoleRepository } from '../domain/repositories/role.repository';
import { DataPermissionUsecases } from './data-permission.usecases';
import { DataRoleUsecases } from './data-role.usecases';
import { ResourceUsecases } from './resource.usecases';
import { RolePermissionUsecases } from './role-permission.usecases';

@Injectable()
export class RoleUsecases {
  constructor(
    private rolePermissionUsecases: RolePermissionUsecases,
    private resourceUsecases: ResourceUsecases,
    private dataPermissionUsecases: DataPermissionUsecases,
    private dataRoleUsecases: DataRoleUsecases,
    @Inject(IRoleRepository)
    private readonly roleRepository: IRoleRepository,
  ) {}
  async createFirstRole(
    data: CreateRoleDto,
    jwtPayload: IAuthUserPayload,
  ): Promise<RoleModel> {
    const roleModel = new RoleModel({
      name: data.name,
      description: data.description,
      parentId: data.parentId,
      allowInherit: data.allowInherit,
      createdBy: jwtPayload?.userId,
      platform: data.platform,
      farmId: data.farmId,
    });

    const role = await this.roleRepository.createFirstRole(roleModel);

    await this.rolePermissionUsecases.createRolePermissionFromResources(
      role.id,
      [...(data.webAdminResources || []), ...(data.webInternalResources || [])],
    );

    return await this.getDetailRole(role.id, jwtPayload);
  }
  async createRole(
    data: CreateRoleDto,
    jwtPayload: IAuthUserPayload,
  ): Promise<RoleModel> {
    const roleModel = new RoleModel({
      name: data.name,
      description: data.description,
      parentId: data.parentId,
      allowInherit: data.allowInherit,
      status: data.status,
      createdBy: jwtPayload?.userId,
      platform: data.platform,
      farmId: data.farmId,
    });

    const role = await this.roleRepository.createRole(roleModel);

    await this.rolePermissionUsecases.createRolePermissionFromResources(
      role.id,
      [...(data.webAdminResources || []), ...(data.webInternalResources || [])],
    );

    await this.handleDataRoleInput(role.id, data);

    return await this.getDetailRole(role.id, jwtPayload);
  }

  private async handleDataRoleInput(
    roleId: string,
    roleDto: CreateRoleDto | UpdateRoleDto,
  ): Promise<void> {
    const dataRoles: CreateDataPermissionDto[] = [];

    if (roleDto.sectors && roleDto.sectors.length) {
      dataRoles.push(...roleDto.sectors);
    }
    if (roleDto.companies && roleDto.companies.length) {
      dataRoles.push(...roleDto.companies);
    }
    if (roleDto.businessUnits && roleDto.businessUnits.length) {
      dataRoles.push(...roleDto.businessUnits);
    }
    if (roleDto.departments && roleDto.departments.length) {
      dataRoles.push(...roleDto.departments);
    }
    if (roleDto.businessOwners && roleDto.businessOwners.length) {
      dataRoles.push(...roleDto.businessOwners);
    }
    if (roleDto.functionUnits && roleDto.functionUnits) {
      dataRoles.push(...roleDto.functionUnits);
    }
    if (roleDto.suppliers && roleDto.suppliers) {
      dataRoles.push(...roleDto.suppliers);
    }
    if (roleDto.materials && roleDto.materials) {
      dataRoles.push(...roleDto.materials);
    }
    if (roleDto.materialGroups && roleDto.materialGroups) {
      dataRoles.push(...roleDto.materialGroups);
    }
    if (roleDto.materialTypes && roleDto.materialTypes) {
      dataRoles.push(...roleDto.materialTypes);
    }
    if (roleDto.positions && roleDto.positions) {
      dataRoles.push(...roleDto.positions);
    }
    if (roleDto.plants && roleDto.plants) {
      dataRoles.push(...roleDto.plants);
    }
    if (roleDto.purchasingGroups && roleDto.purchasingGroups) {
      dataRoles.push(...roleDto.purchasingGroups);
    }
    if (roleDto.purchasingDepartments && roleDto.purchasingDepartments) {
      dataRoles.push(...roleDto.purchasingDepartments);
    }
    if (roleDto.currencyUnits && roleDto.currencyUnits) {
      dataRoles.push(...roleDto.currencyUnits);
    }

    if (roleDto.saleOrgs && roleDto.saleOrgs) {
      dataRoles.push(...roleDto.saleOrgs);
    }

    if (roleDto.divisions && roleDto.divisions) {
      dataRoles.push(...roleDto.divisions);
    }

    if (roleDto.distributionChannels && roleDto.distributionChannels) {
      dataRoles.push(...roleDto.distributionChannels);
    }

    if (roleDto.saleGroups && roleDto.saleGroups) {
      dataRoles.push(...roleDto.saleGroups);
    }

    if (dataRoles.length) {
      await this.dataPermissionUsecases.createDataPermission(
        roleId,
        roleDto.isPersonal,
        dataRoles,
      );
    }
  }

  async updateRole(
    id: string,
    data: UpdateRoleDto,
    jwtPayload: IAuthUserPayload,
  ): Promise<RoleModel> {
    const oldRole = await this.getDetailRole(id, jwtPayload);

    const newRole = Object.assign(oldRole, data);

    await this.roleRepository.updateRole(id, newRole);

    await Promise.all([
      this.rolePermissionUsecases.deleteRolePermissionByRoleId(id),
      this.dataPermissionUsecases.deleteDataPermissionByRoleId(id),
    ]);

    await Promise.all([
      this.rolePermissionUsecases.createRolePermissionFromResources(id, [
        ...(data.webAdminResources || []),
        ...(data.webInternalResources || []),
      ]),
      this.handleDataRoleInput(id, newRole),
    ]);

    return await this.getDetailRole(id, jwtPayload);
  }

  async getDetailRole(
    id: string,
    jwtPayload: IAuthUserPayload,
  ): Promise<RoleModel> {
    const role = await this.checkRoleById(id);

    const { webAdminResources, webInternalResources, dataRoles } =
      await this.getResourcesAndDataRoleWithPlatform(id, jwtPayload?.platform);

    role.webAdminResources = webAdminResources as any;
    role.webInternalResources = webInternalResources as any;

    return { ...role, ...this.handleDataRoleClassification(dataRoles) };
  }

  async getResourcesAndDataRoleWithPlatform(
    roleId: string,
    platform: EPlatform,
  ) {
    let webAdminResources = [];
    let webInternalResources = [];
    let dataRoles = [];
    switch (platform) {
      case EPlatform.DIGI_AQUA: {
        [webAdminResources, webInternalResources, dataRoles] =
          await Promise.all([
            this.resourceUsecases.getResourcesWithRolePermission(
              roleId,
              EPlatform.DIGI_AQUA,
              EPortal.WEB_ADMIN,
            ),
            this.resourceUsecases.getResourcesWithRolePermission(
              roleId,
              EPlatform.DIGI_AQUA,
              EPortal.WEB_INTERNAL_PORTAL,
            ),
            this.dataRoleUsecases.getDataRoleWithDataPermission(
              roleId,
              EPlatform.DIGI_AQUA,
            ),
          ]);
        break;
      }
      case EPlatform.DIGI_SALE: {
        [webAdminResources, webInternalResources, dataRoles] =
          await Promise.all([
            this.resourceUsecases.getResourcesWithRolePermission(
              roleId,
              EPlatform.DIGI_SALE,
              EPortal.WEB_ADMIN,
            ),
            this.resourceUsecases.getResourcesWithRolePermission(
              roleId,
              EPlatform.DIGI_SALE,
              EPortal.WEB_INTERNAL_PORTAL,
            ),
            this.dataRoleUsecases.getDataRoleWithDataPermission(
              roleId,
              EPlatform.DIGI_SALE,
            ),
          ]);

        break;
      }
      case EPlatform.E_PURCHASE: {
        [webAdminResources, webInternalResources, dataRoles] =
          await Promise.all([
            this.resourceUsecases.getResourcesWithRolePermission(
              roleId,
              EPlatform.E_PURCHASE,
              EPortal.WEB_ADMIN,
            ),
            this.resourceUsecases.getResourcesWithRolePermission(
              roleId,
              EPlatform.E_PURCHASE,
              EPortal.WEB_INTERNAL_PORTAL,
            ),
            this.dataRoleUsecases.getDataRoleWithDataPermission(
              roleId,
              EPlatform.E_PURCHASE,
            ),
          ]);

        break;
      }
    }

    return { webAdminResources, webInternalResources, dataRoles };
  }

  async getRoles(
    conditions: GetRoleListDto,
    platform: EPlatform,
  ): Promise<ResponseDto<RoleModel>> {
    const data = await this.roleRepository.getRoles({
      ...conditions,
      platforms: [platform],
    });

    if (data.results && data.results.length) {
      for (let i = 0; i < data.results.length; i++) {
        const dataRoles =
          await this.dataRoleUsecases.getDataRoleWithDataPermission(
            data.results[i].id,
            platform,
          );

        data.results[i] = {
          ...data.results[i],
          ...this.handleDataRoleClassification(dataRoles),
        };
      }
    }

    return data;
  }

  async deleteRole(id: string): Promise<void> {
    await this.checkRoleById(id);

    const countAssignedUserToRole =
      await this.roleRepository.countUsersByRoleId(id);

    if (countAssignedUserToRole > 0) {
      throw new HttpException(
        roleErrorDetails.E_1002(),
        HttpStatus.BAD_REQUEST,
      );
    }

    await Promise.all([
      this.rolePermissionUsecases.deleteRolePermissionByRoleId(id),
      this.dataPermissionUsecases.deleteDataPermissionByRoleId(id),
    ]);

    await this.roleRepository.deleteRole(id);
  }

  async getRolesByUserId(userId: string): Promise<RoleModel[]> {
    return (await this.roleRepository.getRolesByUserId(userId)) || [];
  }

  async getScopes(userId: string, platform: EPlatform) {
    const roles = await this.getRolesByUserId(userId);

    const scopes = [];

    for (const role of roles) {
      if (role.status === EStatus.ACTIVE) {
        const scope = {};

        const { webAdminResources, webInternalResources, dataRoles } =
          await this.getResourcesAndDataRoleWithPlatform(role.id, platform);

        const resource = [...webAdminResources, ...webInternalResources];

        const scopeResources = [];
        resource.map((item: any) => {
          let scopeParent = item.resourceAliases;
          item.rolePermissions.map((action) => {
            if (action.roleId != item.id && action.resourceAction) {
              scopeParent = `${scopeParent}:${action.resourceAction.actionAliases}`;
            }
            // actions tool
            if (action.parentId != null) {
              const isExecute =
                action.actionView ||
                action.actionCreate ||
                action.actionEdit ||
                action.actionDelete;

              if (isExecute)
                scopeResources.push(
                  `${scopeParent}:${action.resourceAliases}:execute`.toLowerCase(),
                );
            } else {
              // resource action
              if (action.actionView)
                scopeResources.push(`${scopeParent}:view`.toLowerCase());
              if (action.actionCreate)
                scopeResources.push(`${scopeParent}:create`.toLowerCase());
              if (action.actionEdit)
                scopeResources.push(`${scopeParent}:edit`.toLowerCase());
              if (action.actionDelete)
                scopeResources.push(`${scopeParent}:delete`.toLowerCase());
            }
          });
        });

        if (scopeResources.length) {
          scope['resources'] = scopeResources.join(',');
        }

        const sectors = [];
        const companies = [];
        const businessUnits = [];
        const departments = [];
        const functionUnits = [];
        const businessOwners = [];
        const suppliers = [];
        const materials = [];
        const materialGroups = [];
        const materialTypes = [];
        const plants = [];
        const positions = [];
        const purchasingDepartments = [];
        const purchasingGroups = [];
        const currencyUnits = [];
        const saleOrgs = [];
        const divisions = [];
        const distributionChannels = [];
        const saleGroups = [];
        const prTypeCodes = [];
        const poTypeCodes = [];
        const costs = [];

        dataRoles?.map((item: any) => {
          item.dataPermissions?.map((permission) => {
            if (permission.allowView) {
              switch (item.type) {
                case DataRoleType.SECTOR: {
                  sectors.push(item.refCode);
                  break;
                }
                case DataRoleType.COMPANY: {
                  companies.push(item.refCode);
                  break;
                }
                case DataRoleType.BUSINESS_UNIT: {
                  businessUnits.push(item.refCode);
                  break;
                }
                case DataRoleType.DEPARTMENT: {
                  departments.push(item.refCode);
                  break;
                }
                case DataRoleType.FUNCTION_UNIT: {
                  functionUnits.push(item.refCode);
                  break;
                }
                case DataRoleType.BUSINESS_OWNER: {
                  businessOwners.push(item.refCode);
                  break;
                }
                case DataRoleType.SUPPLIER: {
                  suppliers.push(item.refCode);
                  break;
                }
                case DataRoleType.MATERIAL: {
                  materials.push(item.refCode);
                  break;
                }
                case DataRoleType.MATERIAL_GROUP: {
                  materialGroups.push(item.refCode);
                  break;
                }
                case DataRoleType.MATERIAL_TYPE: {
                  materialTypes.push(item.refCode);
                  break;
                }
                case DataRoleType.PLANT: {
                  plants.push(item.refCode);
                  break;
                }
                case DataRoleType.POSITION: {
                  positions.push(item.refCode);
                  break;
                }
                case DataRoleType.PURCHASING_DEPARTMENT: {
                  purchasingDepartments.push(item.refCode);
                  break;
                }
                case DataRoleType.PURCHASING_GROUP: {
                  purchasingGroups.push(item.refCode);
                  break;
                }
                case DataRoleType.CURRENCY_UNIT: {
                  currencyUnits.push(item.refCode);
                  break;
                }
                case DataRoleType.SALE_ORG: {
                  saleOrgs.push(item.refCode);
                  break;
                }
                case DataRoleType.DIVISION: {
                  divisions.push(item.refCode);
                  break;
                }
                case DataRoleType.DISTRIBUTION_CHANNEL: {
                  distributionChannels.push(item.refCode);
                  break;
                }
                case DataRoleType.SALE_GROUP: {
                  saleGroups.push(item.refCode);
                  break;
                }
                case DataRoleType.PR_TYPE: {
                  prTypeCodes.push(item.refCode);
                  break;
                }
                case DataRoleType.PURCHASE_ORDER_TYPE: {
                  poTypeCodes.push(item.refCode);
                  break;
                }
                case DataRoleType.COST: {
                  costs.push(item.refCode);
                  break;
                }
              }
            }
          });
        });

        if (sectors.length) {
          scope['sectors'] = sectors.join(',');
        }

        if (companies.length) {
          scope['companies'] = companies.join(',');
        }

        if (businessUnits.length) {
          scope['businessUnits'] = businessUnits.join(',');
        }

        if (departments.length) {
          scope['departments'] = departments.join(',');
        }

        if (functionUnits.length) {
          scope['functionUnits'] = functionUnits.join(',');
        }

        if (businessOwners.length) {
          scope['businessOwners'] = businessOwners.join(',');
        }

        if (suppliers.length) {
          scope['suppliers'] = suppliers.join(',');
        }

        if (materials.length) {
          scope['materials'] = materials.join(',');
        }

        if (materialGroups.length) {
          scope['materialGroups'] = materialGroups.join(',');
        }

        if (materialTypes.length) {
          scope['materialTypes'] = materialTypes.join(',');
        }

        if (plants.length) {
          scope['plants'] = plants.join(',');
        }

        if (positions.length) {
          scope['positions'] = positions.join(',');
        }

        if (purchasingDepartments.length) {
          scope['purchasingDepartments'] = purchasingDepartments.join(',');
        }

        if (purchasingGroups.length) {
          scope['purchasingGroups'] = purchasingGroups.join(',');
        }

        if (currencyUnits.length) {
          scope['currencyUnits'] = currencyUnits.join(',');
        }

        if (saleOrgs.length) {
          scope['saleOrgs'] = saleOrgs.join(',');
        }

        if (divisions.length) {
          scope['divisions'] = divisions.join(',');
        }

        if (distributionChannels.length) {
          scope['distributionChannels'] = distributionChannels.join(',');
        }

        if (saleGroups.length) {
          scope['saleGroups'] = saleGroups.join(',');
        }

        if (prTypeCodes.length) {
          scope['prTypeCodes'] = prTypeCodes.join(',');
        }

        if (poTypeCodes.length) {
          scope['poTypeCodes'] = poTypeCodes.join(',');
        }

        if (costs.length) {
          scope['costs'] = costs.join(',');
        }

        if (Object.keys(scope).length) {
          scopes.push(scope);
        }
      }
    }

    return scopes;
  }

  private handleDataRoleClassification(dataRoles: any) {
    const sectors = [];
    const companies = [];
    const businessUnits = [];
    const departments = [];
    const functionUnits = [];
    const businessOwners = [];
    const suppliers = [];
    const materials = [];
    const materialGroups = [];
    const materialTypes = [];
    const plants = [];
    const positions = [];
    const purchasingDepartments = [];
    const purchasingGroups = [];
    const currencyUnits = [];
    const saleOrgs = [];
    const divisions = [];
    const distributionChannels = [];
    const saleGroups = [];
    const prTypeCodes = [];
    const poTypeCodes = [];
    const costs = [];

    dataRoles?.map((item: any) => {
      item.dataPermissions?.map((permission) => {
        // if (permission.allowView) {
        const dataRole = {
          ...item,
          allowView: permission.allowView,
        };
        delete dataRole.dataPermissions;
        switch (item.type) {
          case DataRoleType.SECTOR: {
            sectors.push(dataRole);
            break;
          }
          case DataRoleType.COMPANY: {
            companies.push(dataRole);
            break;
          }
          case DataRoleType.BUSINESS_UNIT: {
            businessUnits.push(dataRole);
            break;
          }
          case DataRoleType.DEPARTMENT: {
            departments.push(dataRole);
            break;
          }
          case DataRoleType.FUNCTION_UNIT: {
            functionUnits.push(dataRole);
            break;
          }
          case DataRoleType.BUSINESS_OWNER: {
            businessOwners.push(dataRole);
            break;
          }
          case DataRoleType.SUPPLIER: {
            suppliers.push(dataRole);
            break;
          }
          case DataRoleType.MATERIAL: {
            materials.push(dataRole);
            break;
          }
          case DataRoleType.MATERIAL_GROUP: {
            materialGroups.push(dataRole);
            break;
          }
          case DataRoleType.MATERIAL_TYPE: {
            materialTypes.push(dataRole);
            break;
          }
          case DataRoleType.PLANT: {
            plants.push(dataRole);
            break;
          }
          case DataRoleType.POSITION: {
            positions.push(dataRole);
            break;
          }
          case DataRoleType.PURCHASING_DEPARTMENT: {
            purchasingDepartments.push(dataRole);
            break;
          }
          case DataRoleType.PURCHASING_GROUP: {
            purchasingGroups.push(dataRole);
            break;
          }
          case DataRoleType.CURRENCY_UNIT: {
            currencyUnits.push(dataRole);
            break;
          }
          case DataRoleType.SALE_ORG: {
            saleOrgs.push(dataRole);
            break;
          }
          case DataRoleType.DIVISION: {
            divisions.push(dataRole);
            break;
          }
          case DataRoleType.DISTRIBUTION_CHANNEL: {
            distributionChannels.push(dataRole);
            break;
          }
          case DataRoleType.SALE_GROUP: {
            saleGroups.push(dataRole);
            break;
          }
          case DataRoleType.PR_TYPE: {
            prTypeCodes.push(dataRole);
            break;
          }
          case DataRoleType.PURCHASE_ORDER_TYPE: {
            poTypeCodes.push(dataRole);
            break;
          }
          case DataRoleType.COST: {
            costs.push(dataRole);
            break;
          }
        }
        // }
      });
    });

    return {
      sectors,
      companies,
      businessUnits,
      departments,
      functionUnits,
      businessOwners,
      suppliers,
      materials,
      materialGroups,
      materialTypes,
      plants,
      positions,
      purchasingDepartments,
      purchasingGroups,
      currencyUnits,
      saleOrgs,
      divisions,
      distributionChannels,
      saleGroups,
      prTypeCodes,
      poTypeCodes,
      costs,
    };
  }

  async checkRoleById(id: string): Promise<RoleModel> {
    const role = await this.roleRepository.getRoleById(id);

    if (!role) {
      throw new HttpException(
        roleErrorDetails.E_1001(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return role;
  }

  async getRoleByCodes(
    codes: string[],
    platform: string,
  ): Promise<RoleModel[]> {
    return await this.roleRepository.getRoleByCodes(codes, platform);
  }

  async getRolesForDDL(farmId: string) {
    return {
      results: await this.roleRepository.getRolesForDDL(farmId),
    };
  }

  async getAll() {
    return {
      results: await this.roleRepository.getAll(EPlatform.DIGI_AQUA),
    };
  }

  async getByUser(userId: string, jwtPayload: IAuthUserPayload) {
    return {
      results: await this.roleRepository.getByUser(userId, jwtPayload.platform),
    };
  }

  async getRoleAllData() {
    const role = await this.roleRepository.getRoleAllData();

    if (!role) {
      throw new HttpException(
        roleErrorDetails.E_1001(),
        HttpStatus.BAD_REQUEST,
      );
    }

    return role;
  }
}
