import { Inject, Injectable } from '@nestjs/common';
import { ImportUserDto } from '../controller/import/dtos/import-user.dto';
import {
  EFileImportStatus,
  EFileImportType,
} from '../domain/config/enums/file-import-history.enum';
import { FileImportHistoryModel } from '../domain/model/file-import-history.model';
import { UserModel } from '../domain/model/user.model';
import { IUserRepository } from '../domain/repositories/user.repository';
import { AccountUsecases } from './account.usecase';
import { EmailUsecases } from './email.usecases';
import { FileImportHistoryUsecases } from './file-import-history.usecases';
import { UserUsecases } from './user.usecase';
import { readFileSync } from 'fs';
import { resolve } from 'path';
import { EPlatform } from '../domain/config/enums/platform.enum';

@Injectable()
export class ImportUsecases {
  constructor(
    @Inject(IUserRepository)
    private readonly userRepository: IUserRepository,
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
    private readonly userUsecases: UserUsecases,
    private readonly emailUsecases: EmailUsecases,
    private readonly accountUsecases: AccountUsecases,
  ) {}

  async import(
    body: ImportUserDto,
    type: EFileImportType,
    authorization: string,
  ) {
    try {
      switch (type) {
        case EFileImportType.USER:
          const userModels: UserModel[] = [];
          ///Create user
          for (
            let i = 0;
            i < ((body as ImportUserDto).dataUsers?.length ?? 0);
            i++
          ) {
            const userDto = (body as ImportUserDto).dataUsers[i];

            const userModel = new UserModel({
              userName: userDto.userName,
              email: userDto.email,
              password: 'Aa123456@',
              phone: userDto.phone,
              status: userDto.status,
              isNeedOtp: userDto.isNeedOtp ?? false,
              isSuperAdmin: userDto.isSuperAdmin ?? false,
              roles: await this.userUsecases.assignRoleToUser(userDto.roleIds),
              createdBy: (body as ImportUserDto).userInfo?.userId,
              accounts: await this.accountUsecases.createMultiAccount(
                userDto.accounts,
                userDto,
                (body as ImportUserDto).userInfo,
              ),
            });

            userModels.push(userModel);

            await this.userRepository.createUser(userModel);
          }

          for (
            let i = 0;
            i < ((body as ImportUserDto).dataUpdateUsers?.length ?? 0);
            i++
          ) {
            const userDto = (body as ImportUserDto).dataUpdateUsers[i];

            const userModel = new UserModel({
              id: userDto.id,
              userName: undefined,
              email: undefined,
              password: undefined,
              phone: userDto.phone,
              status: userDto.status,
              roles: await this.userUsecases.assignRoleToUser(userDto.roleIds),
              createdBy: (body as ImportUserDto).userInfo?.userId,
              accounts: await this.accountUsecases.createMultiAccount(
                userDto.accounts,
                userDto,
                (body as ImportUserDto).userInfo,
              ),
            });

            await this.userRepository.updateUser(userModel);
          }

          const titleEmail =
            body.userInfo?.platform === EPlatform.E_PURCHASE
              ? 'ePurchase: account information'
              : body.userInfo?.platform === EPlatform.DIGI_SALE
              ? 'DigiSales: Thông tin tài khoản'
              : 'ePurchase: account information';

          const linkTemplate =
            body.userInfo?.platform === EPlatform.E_PURCHASE
              ? '../domain/email_templates/user-information.ejs'
              : body.userInfo?.platform === EPlatform.DIGI_SALE
              ? '../domain/email_templates/user-information-digisale.ejs'
              : '../domain/email_templates/user-information.ejs';

          const linkLogin =
            body.userInfo?.platform === EPlatform.E_PURCHASE
              ? process.env.IDENTITY_LOGIN_API_URL
              : body.userInfo?.platform === EPlatform.DIGI_SALE
              ? process.env.IDENTITY_LOGIN_DIGISALE_API_URL
              : process.env.IDENTITY_LOGIN_DIGIAQUA_API_URL;

          ///Send email user info
          for (let i = 0; i < (userModels.length ?? 0); i++) {
            if (body.userInfo?.platform != EPlatform.E_PURCHASE) {
              const user = userModels[i];
              this.emailUsecases.sendEmail(
                user.email,
                titleEmail,
                readFileSync(resolve(__dirname, linkTemplate), 'utf8'),
                {
                  userName: user.userName,
                  password: user.password,
                  linkLogin: linkLogin,
                },
                authorization,
              );
            }
          }
          break;
        default:
          break;
      }

      const updateFileImportHistory = new FileImportHistoryModel({
        status: EFileImportStatus.SUCCESS,
      });
      await this.fileImportHistoryUsecases.updateFileImportHistory(
        body.fileImportHistoryId,
        updateFileImportHistory,
      );
      console.log(587, 'Import excel done');
    } catch (error) {
      console.log(1500, '===== Import excel failed =====');
      console.log(97, error);
      const updateFileImportHistory = new FileImportHistoryModel({
        errors: error,
        status: EFileImportStatus.FAIL,
      });
      await this.fileImportHistoryUsecases.updateFileImportHistory(
        body.fileImportHistoryId,
        updateFileImportHistory,
      );

      throw error;
    }
  }
}
