import { Inject, Injectable } from '@nestjs/common';
import { CreateDataPermissionDto } from '../controller/role/dtos/create-data-permission.dto';
import { DataPermissionModel } from '../domain/model/data-permission.model';
import { IDataPermissionRepository } from '../domain/repositories/data-permission.repository';
import { DataRoleUsecases } from './data-role.usecases';

@Injectable()
export class DataPermissionUsecases {
  constructor(
    private dataRoleUsecases: DataRoleUsecases,
    @Inject(IDataPermissionRepository)
    private readonly dataPermissionRepository: IDataPermissionRepository,
  ) {}
  async createDataPermission(
    roleId: string,
    isPersonal: boolean,
    dataPermissions: CreateDataPermissionDto[],
  ): Promise<DataPermissionModel[]> {
    const dataPermissionModels: DataPermissionModel[] = [];

    if (isPersonal) {
      dataPermissionModels.push(
        new DataPermissionModel({
          roleId,
          isPersonal,
          dataRoleId: null,
        }),
      );
    } else {
      for (const dataPermission of dataPermissions) {
        const { dataRoleId } = dataPermission;

        await this.dataRoleUsecases.getDataRoleById(dataRoleId);

        dataPermissionModels.push(
          new DataPermissionModel({
            roleId,
            dataRoleId,
            allowView: dataPermission.allowView,
          }),
        );
      }
    }
    return await this.dataPermissionRepository.createDataPermissions(
      dataPermissionModels,
    );
  }

  async deleteDataPermissionByRoleId(roleId: string): Promise<void> {
    await this.dataPermissionRepository.deleteDataPermissionByRoleId(roleId);
  }
}
