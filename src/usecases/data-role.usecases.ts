import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import Graph from 'graph-data-structure';
import { CreateDataRoleDto } from '../controller/data-role/dtos/create-data-role.dto';
import { GetDataRoleListDto } from '../controller/data-role/dtos/get-data-role-list.dto';
import { UpdateDataRoleDto } from '../controller/data-role/dtos/update-data-role.dto';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { ResponseDto } from '../domain/dtos/response.dto';
import { DataRoleModel } from '../domain/model/data-role.model';
import { IDataRoleRepository } from '../domain/repositories/data-role.repository';
import { merge } from '../utils/common';

@Injectable()
export class DataRoleUsecases {
  constructor(
    @Inject(IDataRoleRepository)
    private readonly dataRoleRepository: IDataRoleRepository,
  ) {}
  async createDataRole(data: CreateDataRoleDto): Promise<DataRoleModel> {
    const dataRoleCheck = await this.dataRoleRepository.getDataRoleByRefId(
      data.refId,
    );

    if (!dataRoleCheck) {
      const dataRoleModel = new DataRoleModel({
        refCode: data.refCode,
        refId: data.refId,
        description: data.description,
        parentId: data.parentId || null,
        type: data.type,
        platform: data.platform,
      });

      return await this.dataRoleRepository.createDataRole(dataRoleModel);
    } else {
      return await this.dataRoleRepository.updateDataRole(
        dataRoleCheck.id,
        data,
      );
    }
  }

  async getDataRoleById(id: string): Promise<DataRoleModel> {
    const dataRole = await this.dataRoleRepository.getDataRoleById(id);

    if (!dataRole) {
      throw new HttpException(
        { message: 'DATA_ROLE_NOT_FOUND' },
        HttpStatus.BAD_REQUEST,
      );
    }

    return dataRole;
  }

  async getDataRoles(
    conditions: GetDataRoleListDto,
  ): Promise<ResponseDto<DataRoleModel>> {
    return await this.dataRoleRepository.getDataRoles(conditions);
  }

  async deleteDataRole(refId: string): Promise<void> {
    await this.dataRoleRepository.deleteDataRole(refId);
  }

  async updateDataRole(
    refId: string,
    updateDataRoleDto: UpdateDataRoleDto,
  ): Promise<DataRoleModel> {
    const dataRole = await this.getDataRoleByRefId(refId);
    return await this.dataRoleRepository.updateDataRole(
      dataRole.id,
      updateDataRoleDto,
    );
  }

  async getDataRoleByRoleId(roleId: string): Promise<DataRoleModel[]> {
    return await this.dataRoleRepository.getDataRoleByRoleId(roleId);
  }

  async getDataRoleWithDataPermission(roleId: string, platform: EPlatform) {
    const drp = await this.dataRoleRepository.getDataRoleByRoleId(
      roleId,
      platform,
    );

    const dataRoles = {};
    const dataRoleActions = {};

    drp.forEach((dataRole) => {
      if (dataRole.roleId != roleId || !dataRole.dataRoleId) {
        // continue
        return;
      }

      const prefix = dataRole.dataRoleId;

      if (!dataRoleActions[prefix]) {
        dataRoleActions[prefix] = [];
      }

      if (!dataRole.parentId) {
        dataRoleActions[prefix].push(dataRole.refCode);
        dataRoles[prefix] = {
          id: dataRole.dataRoleId,
          refCode: dataRole.refCode,
          refId: dataRole.refId,
          type: dataRole.type,
          isEnabled: dataRole.isEnabled,
          parentId: dataRole.parentId,
          description: dataRole.description,
          platform: dataRole.platform,
          dataPermissions: dataRoleActions[prefix],
        };
      } else {
        if (!dataRoleActions[dataRole.parentId]) {
          dataRoleActions[dataRole.parentId] = [];
        }

        dataRoleActions[dataRole.parentId].push(dataRole.refCode);

        if (!dataRoles[dataRole.parentId]) {
          dataRoles[dataRole.parentId] = {};
        }
        dataRoles[dataRole.parentId].dataPermissions =
          dataRoleActions[dataRole.parentId];
      }
    });

    for (const key of Object.keys(dataRoles)) {
      dataRoles[key].dataPermissions = Object.values(
        this.dataRoleInherit(drp, dataRoles[key]),
      );
    }

    return Object.values(dataRoles);
  }

  private dataRoleInherit(dataRoles, dataRoleCheck) {
    const graph = Graph();

    let firstDepth = -1;
    let currentDepth = -1;
    const nodesMapDepth = {};
    const nodesProperties = {};
    const nodesMapInherit = {};

    dataRoles.forEach((dataRole) => {
      if (firstDepth == -1) firstDepth = dataRole.nlevel;
      const prefix = `${dataRole.parentId ?? dataRole.dataRoleId}_${
        dataRole.refCode
      }`;

      currentDepth = dataRole.nlevel;

      graph.addNode(currentDepth + '_' + prefix);

      nodesProperties[currentDepth + '_' + prefix] = dataRole;
      nodesMapInherit[currentDepth] = dataRole.allowInherit;

      if (nodesMapDepth[prefix] && nodesMapDepth[prefix] != currentDepth) {
        graph.addEdge(
          nodesMapDepth[prefix] + '_' + prefix,
          currentDepth + '_' + prefix,
        );
      }

      nodesMapDepth[prefix] = currentDepth;
    });

    let dr = {};

    dataRoleCheck.dataPermissions.forEach((dp) => {
      const prefix = `${dataRoleCheck.id}${dp ? '_' + dp : ''}`;

      const drSearch = graph.depthFirstSearch([`${firstDepth}_${prefix}`]);

      if (nodesProperties[drSearch[0]]) {
        const listInherit = this.fetchDataPermissionInherit(
          drSearch,
          nodesProperties,
        );

        dr = merge(dr, listInherit);
      }
    });

    return dr;
  }

  private parseView(allowInherit, inherit, action) {
    const actionVal =
      (inherit && action) == false
        ? allowInherit && !inherit
          ? null
          : !allowInherit
          ? action
          : false
        : inherit && action;

    return !allowInherit && actionVal == null ? false : actionVal;
  }

  private dataPermissionFormat(permission) {
    return {
      id: permission.id,
      allowView: permission.allowView,
      roleId: permission.roleId,
      dataRoleId: permission.dataRoleId,
      refCode: permission.refCode,
      type: permission.type,
      allowInherit: permission.allowInherit,
      parentId: permission.parentId,
      platform: permission.platform,
    };
  }

  private fetchDataPermissionInherit(permissionList, nodesProps) {
    const mergePermissionsInherit = {};
    const flagFirstPermissionsInherit = {};

    permissionList.reverse().forEach((permission) => {
      const ob = nodesProps[permission];

      const prefix = `${ob.id}${ob.parentId ? '_' + ob.refCode : ''}`;
      if (!flagFirstPermissionsInherit[prefix]) {
        flagFirstPermissionsInherit[prefix] = ob;
      }

      ob.allowView = ob.allowView ?? false;

      mergePermissionsInherit[prefix] = {
        allowView: this.parseView(
          ob.allowInherit,
          mergePermissionsInherit[prefix]?.allowView,
          ob.allowView,
        ),
      };

      mergePermissionsInherit[prefix] = this.dataPermissionFormat(
        merge(
          flagFirstPermissionsInherit[prefix],
          mergePermissionsInherit[prefix],
        ),
      );

      if (!ob.allowInherit) {
        return;
      }
    });

    return mergePermissionsInherit;
  }

  async getDataRoleByRefId(refId: string): Promise<DataRoleModel> {
    return await this.dataRoleRepository.getDataRoleByRefId(refId);
  }
}
