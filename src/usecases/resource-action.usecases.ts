import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { CreateResourceDto } from '../controller/resource/dtos/create-resource.dto';
import { GetResourceActionListDto } from '../controller/resource/dtos/get-resource-action.dto';
import { ResponseDto } from '../domain/dtos/response.dto';
import { ResourceActionModel } from '../domain/model/resource-action.model';
import { IResourceActionRepository } from '../domain/repositories/resource-action.repository';
import { ResourceUsecases } from './resource.usecases';

@Injectable()
export class ResourceActionUsecases {
  constructor(
    private resourceUsecases: ResourceUsecases,
    @Inject(IResourceActionRepository)
    private readonly resourceActionRepository: IResourceActionRepository,
  ) {}
  async createResourceAction(
    data: CreateResourceDto,
  ): Promise<ResourceActionModel> {
    const resource = await this.resourceUsecases.createResource(data);

    const action = new ResourceActionModel({
      resourceId: resource.id,
      actionAliases: data.resourceAliases,
      description: data.description,
      resourceIdGroup: data.parentId,
    });

    return await this.resourceActionRepository.createResourceAction(action);
  }

  async getResourceActionById(id: string): Promise<ResourceActionModel> {
    const action =
      await this.resourceActionRepository.getResourceActionById(id);

    if (!action) {
      throw new HttpException(
        { message: 'RESOURCE_ACTION_NOT_FOUND' },
        HttpStatus.BAD_REQUEST,
      );
    }

    return action;
  }

  async getResourceActions(
    conditions: GetResourceActionListDto,
  ): Promise<ResponseDto<ResourceActionModel>> {
    return await this.resourceActionRepository.getResourceActions(conditions);
  }

  async deleteResourcAction(id: string): Promise<void> {
    await this.resourceActionRepository.deleteResourceAction(id);
  }
}
