import {
  HttpException,
  HttpStatus,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService, TokenExpiredError } from '@nestjs/jwt';
import axios from 'axios';
import * as bcrypt from 'bcryptjs';
import { readFileSync } from 'fs';
import { resolve } from 'path';
import {
  ChangePasswordByAdminDto,
  ChangePasswordDto,
} from '../controller/auth/dtos/change-password.dto';
import { ForgotPasswordDto, LoginDto } from '../controller/auth/dtos/login.dto';
import { ResendOtpDto, VerifyOtpDto } from '../controller/auth/dtos/otp.dto';
import { ResetPasswordDto } from '../controller/auth/dtos/reset-password.dto';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { EPortal } from '../domain/config/enums/portal.enum';
import { EStatus } from '../domain/config/enums/status.enum';
import { ETokenType } from '../domain/config/enums/token-type.enum';
import { IAuthUserPayload } from '../domain/interface/auth-user-payload.interface';
import { IVerifyOtp } from '../domain/interface/verify-otp.interface';
import { authErrorDetails } from '../domain/messages/error-detail/auth';
import { UserVerifyModel } from '../domain/model/user-verify.model';
import { UserModel } from '../domain/model/user.model';
import { EnvironmentConfigService } from '../infrastructure/config/environment-config/environment-config.service';
import { genOtpCode } from '../utils/common';
import { getData } from '../utils/http';
import { EmailUsecases } from './email.usecases';
import { RedisUsecases } from './redis.usecases';
import { RoleUsecases } from './role.usecases';
import { UserVerifyUsecases } from './user-verify.usecase';
import { UserUsecases } from './user.usecase';

@Injectable()
export class authUsecases {
  constructor(
    private roleUsecases: RoleUsecases,
    private userUsecases: UserUsecases,
    private userVerifyUsecases: UserVerifyUsecases,
    private readonly _jwtService: JwtService,
    private emailUsecases: EmailUsecases,
    private config: EnvironmentConfigService = new EnvironmentConfigService(),
    private readonly redisUsecases: RedisUsecases,
  ) {}

  async login(
    login: LoginDto,
    authorization: string,
  ): Promise<
    | (IAuthUserPayload & { accessToken: string; refreshToken: string })
    | IVerifyOtp
  > {
    const userInfo = await this.userUsecases.getUserByUserName(
      login.userName,
      login.platform,
    );

    if (!userInfo) {
      throw new HttpException(
        authErrorDetails.E_0101('ACCOUNT_NOT_AVAILABLE'),
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.verifyPortalByRoles(userInfo.id, login.platform, login.portal);

    const match = await bcrypt.compare(login.password, userInfo.password);
    if (!match) {
      throw new HttpException(
        authErrorDetails.E_0102('USER_NAME_PASSWORD_INVALID'),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (userInfo.status !== EStatus.ACTIVE) {
      throw new HttpException(
        authErrorDetails.E_0103('USER_IN_ACTIVE'),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (userInfo.isNeedOtp) {
      const userVerify = await this.userVerifyUsecases.getUserVerify(
        userInfo.id,
        ETokenType.VERIFY_OTP,
      );

      ///Save data user verify
      const verifyToken = await this.saveUserVerifyAndSendMail(
        userInfo,
        ETokenType.VERIFY_OTP,
        authorization,
        userVerify,
        login.portal,
      );
      return {
        token: verifyToken,
        isNeedOtp: userInfo.isNeedOtp,
        email: userInfo.email,
      };
    }

    return await this.generateAuthResponse(
      ETokenType.ACCESS,
      userInfo,
      login.portal,
    );
  }
  async refresh(refreshToken: string): Promise<any> {
    try {
      const payload = await this._jwtService.verifyAsync(refreshToken, {
        secret: process.env.JWT_SECRET,
      });
      console.log(114, payload);
      if (payload.tokenType !== ETokenType.REFRESH) {
        throw new HttpException(
          authErrorDetails.E_0104('TOKEN_IS_INCORRECT'),
          HttpStatus.FORBIDDEN,
        );
      }

      const userInfo = await this.userUsecases.getDetailUser(
        payload.userId,
        payload.platform,
      );

      const authUserPayload: IAuthUserPayload = {
        tokenType: ETokenType.ACCESS,
        userId: userInfo.id,
        firstName: userInfo.accounts[0].firstName,
        lastName: userInfo.accounts[0].lastName,
        staffCode: userInfo.accounts[0].staffCode,
        staffId: userInfo.accounts[0].staffId,
        email: userInfo.email,
        phone: userInfo.phone,
        isSuperAdmin: userInfo.isSuperAdmin,
        isNeedOtp: userInfo.isNeedOtp,
        platform: payload.platform,
        portal: payload.portal,
        isAdmin: userInfo.accounts[0].isAdmin,
        scopes:
          (await this.roleUsecases.getScopes(
            userInfo.id,
            userInfo.accounts[0].platform,
          )) || [],
      };

      const data = {
        ...authUserPayload,
        accessToken: await this._jwtService.signAsync(
          {
            ...authUserPayload,
            scopes:
              payload.platform != EPlatform.E_PURCHASE
                ? // &&
                  // payload.platform != EPlatform.DIGI_SALE
                  authUserPayload.scopes
                : undefined,
          },
          {
            expiresIn: Number(process.env.JWT_EXPIRATION_TIME),
          },
        ),
        refreshToken: await this._jwtService.signAsync(
          {
            tokenType: ETokenType.REFRESH,
            userId: userInfo.id,
            platform: payload.platform,
            portal: payload.portal,
          },
          {
            expiresIn: Number(process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME),
          },
        ),
      };

      const cachedAccessToken = await this.redisUsecases.get(refreshToken);

      if (cachedAccessToken) {
        await this.redisUsecases.remove(cachedAccessToken);
      }

      await Promise.all([
        this.redisUsecases.set(
          data.accessToken,
          JSON.stringify(authUserPayload.scopes),
          process.env.JWT_EXPIRATION_TIME
            ? Number(process.env.JWT_EXPIRATION_TIME)
            : null,
        ),
        this.redisUsecases.set(
          data.refreshToken,
          data.accessToken,
          process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME
            ? Number(process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME)
            : null,
        ),
        this.redisUsecases.remove(refreshToken),
      ]);

      return data;
    } catch (error) {
      throw new HttpException(
        authErrorDetails.E_0104('TOKEN_IS_INCORRECT'),
        HttpStatus.FORBIDDEN,
      );
    }
  }
  // async loginByAzureAD(
  //   user: any,
  //   platform: EPlatform = EPlatform.E_PURCHASE,
  //   portal: EPortal = EPortal.WEB_ADMIN,
  // ): Promise<any> {
  //   try {
  //     console.log(180, user);
  //     if (!user) {
  //       throw new HttpException(
  //         authErrorDetails.E_0101('ACCOUNT_NOT_AVAILABLE'),
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }

  //     const userInfo: UserModel = await this.userUsecases.getUserByUserName(
  //       user.email,
  //       platform,
  //     );

  //     if (!userInfo) {
  //       throw new HttpException(
  //         authErrorDetails.E_0101('ACCOUNT_NOT_AVAILABLE'),
  //         HttpStatus.BAD_REQUEST,
  //       );
  //     }

  //     await this.rabbitConfig.publishMessage('user_logged_in', { userInfo });

  //     return await this.generateAuthResponse(
  //       ETokenType.ACCESS,
  //       userInfo,
  //       portal,
  //     );
  //   } catch (error) {
  //     throw new HttpException(
  //       authErrorDetails.E_0101('ACCOUNT_NOT_AVAILABLE'),
  //       HttpStatus.BAD_REQUEST,
  //     );
  //   }
  // }

  async loginByAzureAD(
    idToken: string,
    platform: EPlatform = EPlatform.E_PURCHASE,
    portal: EPortal = EPortal.WEB_ADMIN,
  ): Promise<any> {
    // try {
    const payload: any = this._jwtService.decode(idToken);
    if (!payload) {
      throw new UnauthorizedException();
    }
    const user: UserModel = await this.userUsecases.getUserByUserName(
      payload.preferred_username,
      platform,
    );

    if (!user) {
      throw new HttpException(
        authErrorDetails.E_0101('ACCOUNT_NOT_AVAILABLE'),
        HttpStatus.BAD_REQUEST,
      );
    } else {
      const userInfo: any = await this.userUsecases.getDetailUser(
        user.id,
        platform,
      );

      return await this.generateAuthResponse(
        ETokenType.ACCESS,
        userInfo,
        portal,
      );
    }
    // } catch (error) {
    //   console.log(250, error);
    //   throw new UnauthorizedException();
    // }
  }

  async verifyOtp(verifyOtp: VerifyOtpDto) {
    try {
      const payload: any = await this._jwtService.verifyAsync(
        verifyOtp.verifyToken,
        {
          secret: process.env.JWT_SECRET,
          ignoreExpiration: false,
        },
      );

      if (
        ![ETokenType.VERIFY_OTP, ETokenType.RECOVERY_PASSWORD].includes(
          payload.tokenType,
        )
      ) {
        throw new HttpException(
          authErrorDetails.E_0106('OTP_IS_INCORRECT'),
          HttpStatus.FORBIDDEN,
        );
      }

      const userVerify = await this.userVerifyUsecases.getUserVerify(
        payload.userId,
        payload.tokenType,
      );

      this.checkOtpValidity(verifyOtp, userVerify);

      const userInfo = await this.userUsecases.getDetailUser(payload.userId);

      if (userInfo.status !== EStatus.ACTIVE) {
        throw new HttpException(
          authErrorDetails.E_0103('USER_IN_ACTIVE'),
          HttpStatus.BAD_REQUEST,
        );
      }

      await this.userVerifyUsecases.deleteUserVerify(
        userInfo.id,
        payload.tokenType,
      );

      return this.generateAuthResponse(
        payload.tokenType,
        userInfo,
        payload.portal,
      );
    } catch (error) {
      throw new HttpException(
        authErrorDetails.E_0106('OTP_IS_INCORRECT'),
        HttpStatus.FORBIDDEN,
      );
    }
  }

  private checkOtpValidity(verifyOtp: VerifyOtpDto, userVerify: any) {
    if (
      verifyOtp.verifyToken !== userVerify.token ||
      verifyOtp.otp !== userVerify.otp
    ) {
      throw new HttpException(
        authErrorDetails.E_0106('OTP_IS_INCORRECT'),
        HttpStatus.FORBIDDEN,
      );
    }

    const diffMilliseconds = (Date.now() - userVerify.expireOtpTime) / 1000; // milliseconds
    if (diffMilliseconds > Number(this.config.getOtpExpireTime())) {
      throw new HttpException(
        authErrorDetails.E_0107('OTP_IS_EXPIRED'),
        HttpStatus.BAD_GATEWAY,
      );
    }
  }

  private async generateAuthResponse(
    tokenType: ETokenType,
    userInfo: UserModel,
    portal?: EPortal,
  ): Promise<
    | (IAuthUserPayload & { accessToken: string; refreshToken: string })
    | IVerifyOtp
  > {
    switch (tokenType) {
      case ETokenType.VERIFY_OTP:
      case ETokenType.ACCESS: {
        const authUserPayload: IAuthUserPayload = {
          tokenType: ETokenType.ACCESS,
          userId: userInfo.id,
          firstName: userInfo.accounts[0].firstName,
          lastName: userInfo.accounts[0].lastName,
          staffCode: userInfo.accounts[0].staffCode,
          staffId: userInfo.accounts[0].staffId,
          isSuperAdmin: userInfo.isSuperAdmin,
          isNeedOtp: userInfo.isNeedOtp,
          platform: userInfo.accounts[0].platform,
          portal: portal,
          email: userInfo.email,
          phone: userInfo.phone,
          isAdmin: userInfo.accounts[0].isAdmin || false,
          scopes:
            (await this.roleUsecases.getScopes(
              userInfo.id,
              userInfo.accounts[0].platform,
            )) || [],
        };

        //TODO: tạm thời đóng để build beta
        //Only aqua
        if (userInfo?.accounts[0]?.platform === EPlatform.DIGI_AQUA) {
          const rs = await getData(
            process.env.IDENTITY_AQUA_SERVICE_API_URL +
              `/farm/get-by-user-id/${userInfo.id}`,
          );
          authUserPayload.staffId = rs.data?.data?.staffId;
          if (rs?.data?.data?.farms?.length == 1) {
            authUserPayload.isOneFarm = rs.data?.data.farms[0].id;
          }
        }

        const data = {
          ...authUserPayload,
          accessToken: await this._jwtService.signAsync(
            {
              ...authUserPayload,
              scopes:
                userInfo.accounts[0].platform != EPlatform.E_PURCHASE
                  ? // &&
                    // userInfo.accounts[0].platform != EPlatform.DIGI_SALE
                    authUserPayload.scopes
                  : undefined,
            },
            {
              expiresIn: Number(process.env.JWT_EXPIRATION_TIME),
            },
          ),
          refreshToken: await this._jwtService.signAsync(
            {
              tokenType: ETokenType.REFRESH,
              userId: userInfo.id,
              platform: authUserPayload.platform,
              portal: portal,
            },
            {
              expiresIn: Number(process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME),
            },
          ),
        };

        if (tokenType == ETokenType.ACCESS) {
          console.log(440, 'Set Redis');
          await Promise.all([
            this.redisUsecases.set(
              data.accessToken,
              JSON.stringify(authUserPayload.scopes),
              process.env.JWT_EXPIRATION_TIME
                ? Number(process.env.JWT_EXPIRATION_TIME)
                : null,
            ),
            this.redisUsecases.set(
              data.refreshToken,
              data.accessToken,
              process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME
                ? Number(process.env.JWT_REFRESH_TOKEN_EXPIRATION_TIME)
                : null,
            ),
          ]);
        }

        return data;
      }
      case ETokenType.RECOVERY_PASSWORD:
        const verifyToken = await this._jwtService.signAsync(
          {
            tokenType: ETokenType.FORGOT_PASSWORD,
            userId: userInfo.id,
          },
          {
            expiresIn: Number(this.config.getOtpTokenExpireTime()),
            secret: process.env.JWT_SECRET,
          },
        );
        return { token: verifyToken };
    }
  }

  async resendOtp(resendOtp: ResendOtpDto, authorization: string) {
    let payload;
    try {
      payload = await this._jwtService.verifyAsync(resendOtp.verifyToken, {
        secret: process.env.JWT_SECRET,
        ignoreExpiration: true,
      });
    } catch (error) {
      throw new HttpException(
        authErrorDetails.E_0106('OTP_IS_INCORRECT'),
        HttpStatus.FORBIDDEN,
      );
    }

    if (
      ![ETokenType.VERIFY_OTP, ETokenType.RECOVERY_PASSWORD].includes(
        payload.tokenType,
      )
    ) {
      throw new HttpException(
        authErrorDetails.E_0106('OTP_IS_INCORRECT'),
        HttpStatus.FORBIDDEN,
      );
    }

    const userInfo = await this.userUsecases.getDetailUser(
      payload.userId,
      payload.platform,
    );

    if (userInfo.status !== EStatus.ACTIVE) {
      throw new HttpException(
        authErrorDetails.E_0103('USER_IN_ACTIVE'),
        HttpStatus.BAD_REQUEST,
      );
    }

    const userVerify = await this.userVerifyUsecases.getUserVerify(
      userInfo.id,
      payload.tokenType,
    );

    ///Save data user verify
    const verifyToken = await this.saveUserVerifyAndSendMail(
      userInfo,
      payload.tokenType,
      authorization,
      userVerify,
    );

    return {
      token: verifyToken,
    };
  }

  async forgotPassword(data: ForgotPasswordDto, authorization: string) {
    const userInfo = await this.userUsecases.getUserByEmail(
      data.email,
      null,
      data.platform,
    );

    if (userInfo.status !== EStatus.ACTIVE) {
      throw new HttpException(
        authErrorDetails.E_0103('USER_IN_ACTIVE'),
        HttpStatus.BAD_REQUEST,
      );
    }

    const userVerify = await this.userVerifyUsecases.getUserVerify(
      userInfo.id,
      ETokenType.RECOVERY_PASSWORD,
    );

    ///Save data user verify
    const verifyToken = await this.saveUserVerifyAndSendMail(
      userInfo,
      ETokenType.RECOVERY_PASSWORD,
      authorization,
      userVerify,
    );

    return {
      token: verifyToken,
    };
  }

  async resetPassword(data: ResetPasswordDto) {
    try {
      const payload = await this._jwtService.verifyAsync(data.verifyToken, {
        secret: process.env.JWT_SECRET,
        ignoreExpiration: false,
      });

      if (payload.tokenType === ETokenType.FORGOT_PASSWORD) {
        if (data.password != data.confirmPassword) {
          throw new HttpException(
            authErrorDetails.E_0108('PASSWORD_NOT_MATCH'),
            HttpStatus.BAD_REQUEST,
          );
        }

        const userInfo = await this.userUsecases.getDetailUser(payload.userId);

        await this.userUsecases.updateUserPassword(userInfo.id, data.password);

        // return this.generateAuthResponse(payload.tokenType, userInfo);
      }
    } catch (error) {
      console.log(error);
      if (error instanceof TokenExpiredError) {
        throw new HttpException(
          { message: 'TOKEN_IS_EXPIRED' },
          HttpStatus.FORBIDDEN,
        );
      }

      throw new HttpException(
        { message: 'OTP_IS_INCORRECT', errorCode: 'MS_05' },
        HttpStatus.FORBIDDEN,
      );
    }
  }

  private async validateUserVerify(userVerify: UserVerifyModel) {
    const time = Date.now();
    const diffHour = (time - userVerify.sendOtpTime) / 1000 / 60 / 60; //milliseconds -> hours

    if (diffHour < Number(this.config.getOtpMaxTimeStampSent())) {
      if (userVerify.countOtpTime >= Number(this.config.getOtpMaxTimeSent())) {
        throw new HttpException(
          { message: 'OTP_LIMIT_SEND_IN_2_HOURS' },
          HttpStatus.BAD_REQUEST,
        );
      }
    }
  }

  private async saveUserVerifyAndSendMail(
    userInfo: UserModel,
    tokenType: ETokenType,
    authorization: string,
    userVerify?: UserVerifyModel,
    portal?: EPortal,
  ) {
    ///Tạo verify token
    const verifyToken = await this._jwtService.signAsync(
      {
        tokenType: tokenType,
        userId: userInfo.id,
        platform:
          userInfo?.accounts && userInfo?.accounts.length
            ? userInfo?.accounts[0]?.platform
            : null,
        portal: portal,
      },
      {
        expiresIn: Number(this.config.getOtpTokenExpireTime()),
        secret: process.env.JWT_SECRET,
      },
    );
    ///Thời gian hiện tại milliseconds
    const time = Date.now();
    ///Gửi email với otp code cho user
    const otpCode = genOtpCode();
    if (userVerify) {
      ///Validate user verify
      await this.validateUserVerify(userVerify);
      const diffHour = (time - userVerify.sendOtpTime) / 1000 / 60 / 60; //milliseconds -> hours
      await this.userVerifyUsecases.updateUserVerify({
        userId: userInfo.id,
        otp: otpCode,
        token: verifyToken,
        expireOtpTime: time + Number(this.config.getOtpExpireTime()), //milliseconds
        sendOtpTime:
          diffHour < Number(this.config.getOtpMaxTimeStampSent())
            ? userVerify.sendOtpTime
            : time,
        countOtpTime:
          diffHour < Number(this.config.getOtpMaxTimeStampSent())
            ? userVerify.countOtpTime + 1
            : 1,
        tokenType: tokenType,
      });
    } else {
      ///Lưu thông tin user verify (tạo mới)
      await this.userVerifyUsecases.createUserVerify({
        userId: userInfo.id,
        otp: otpCode,
        token: verifyToken,
        expireOtpTime: time + Number(this.config.getOtpExpireTime()), //milliseconds
        countOtpTime: 1,
        sendOtpTime: time,
        tokenType: tokenType,
      });
    }

    let titleEmail = 'ePurchase: OTP code to verify account';

    if (tokenType === ETokenType.VERIFY_OTP) {
      titleEmail = 'ePurchase: OTP code to verify account';
    } else {
      if (userInfo?.accounts[0]?.platform == EPlatform.DIGI_SALE) {
        titleEmail = 'DigiSales: Mã OTP đặt lại mật khẩu của bạn';
      } else {
        titleEmail = 'ePurchase: OTP code to reset password';
      }
    }

    const linkTemplate =
      userInfo?.accounts[0]?.platform == EPlatform.DIGI_SALE
        ? '../domain/email_templates/user-verify-otp-digisale.ejs'
        : '../domain/email_templates/user-verify-otp.ejs';

    ///Send email otp
    await this.emailUsecases.sendEmail(
      userInfo.email,
      titleEmail,
      readFileSync(resolve(__dirname, linkTemplate), 'utf8'),
      {
        otp: otpCode,
      },
      authorization,
    );

    return verifyToken;
  }

  private async verifyPortalByRoles(
    userId: string,
    platform: EPlatform,
    portal: EPortal,
  ): Promise<void> {
    const roles = await this.roleUsecases.getRolesByUserId(userId);

    if (!roles || !roles.length) {
      throw new HttpException(authErrorDetails.E_0109(), HttpStatus.FORBIDDEN);
    }

    let isPass = false;

    for (const role of roles) {
      const { webAdminResources, webInternalResources } =
        await this.roleUsecases.getResourcesAndDataRoleWithPlatform(
          role.id,
          platform,
        );

      switch (portal) {
        case EPortal.WEB_ADMIN: {
          if (webAdminResources && webAdminResources.length) {
            isPass = true;
          }
          break;
        }
        case EPortal.WEB_INTERNAL_PORTAL: {
          if (webInternalResources && webInternalResources.length) {
            isPass = true;
          }
          break;
        }
      }
    }

    if (!isPass) {
      throw new HttpException(authErrorDetails.E_0109(), HttpStatus.FORBIDDEN);
    }
  }

  async changePassword(dto: ChangePasswordDto, jwtPayload: IAuthUserPayload) {
    const { oldPassword, newPassword, confirmPassword } = dto;

    const userInfo = await this.userUsecases.getDetailUser(
      jwtPayload.userId,
      jwtPayload.platform,
      true,
    );

    const match = await bcrypt.compare(oldPassword, userInfo.password);
    if (!match) {
      throw new HttpException(
        authErrorDetails.E_0102('USER_NAME_PASSWORD_INVALID'),
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if the new password is different from the old password
    if (newPassword === oldPassword) {
      throw new HttpException(
        authErrorDetails.E_0111(),
        HttpStatus.BAD_REQUEST,
      );
    }

    if (newPassword != confirmPassword) {
      throw new HttpException(
        authErrorDetails.E_0108('PASSWORD_NOT_MATCH'),
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.userUsecases.updateUserPassword(userInfo.id, newPassword);
  }

  async changePasswordByAdmin(request: ChangePasswordByAdminDto) {
    const userInfo = await this.userUsecases.getDetailUser(
      request.userId,
      request.platform,
      true,
    );

    await this.userUsecases.updateUserPassword(
      userInfo.id,
      request.newPassword,
    );
  }

  async callbackAzureAd(code: string) {
    if (!code) {
      throw new HttpException(
        'No authorization code received',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      const tokenUrl = this.config.getAzureADAccessUrl();

      const params = new URLSearchParams({
        client_id: this.config.getAzureADClientId(),
        client_secret: this.config.getAzureADClientSecret(),
        code: code as string,
        redirect_uri: 'http://localhost:8000/auth/oauth/callback',
        grant_type: 'authorization_code',
        scope: 'https://outlook.office365.com/SMTP.Send offline_access',
      });

      const response = await axios.post(tokenUrl, params, {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      });

      return response?.data;
    } catch (error: any) {
      throw new HttpException(
        'Token error: ' + error.response?.data || error.message || '',
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
