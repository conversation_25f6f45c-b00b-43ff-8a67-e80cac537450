import { Inject, Injectable } from '@nestjs/common';
import { IMobileManagementRepository } from '../domain/repositories/mobile-management.repository';
import { CreateMobileManagementDto } from '../controller/mobile-management/dtos/create-mobile-management.dto';
import { UpdateMobileManagementDto } from '../controller/mobile-management/dtos/update-mobile-management.dto';
import { EPlatform } from '../domain/config/enums/platform.enum';
import { SocketService } from '../infrastructure/config/socket/socket.service';

@Injectable()
export class MobileManagementUsecases {
  constructor(
    @Inject(IMobileManagementRepository)
    private readonly mobileManagementRepository: IMobileManagementRepository,
    private socketService: SocketService,
  ) {}

  async getDetailMobileManagement(platform: EPlatform) {
    return await this.mobileManagementRepository.getDetailMobileManagement(
      platform,
    );
  }

  async createUpdateMobileManagement(
    data: CreateMobileManagementDto | UpdateMobileManagementDto,
    jwtPayload: any,
  ) {
    data.platform = jwtPayload?.platform;

    const mobileManagement = await this.getDetailMobileManagement(
      jwtPayload?.platform,
    );

    if (mobileManagement) {
      await this.mobileManagementRepository.updateMobileManagement(
        mobileManagement.id,
        data,
      );
    } else {
      await this.mobileManagementRepository.createMobileManagement(data);
    }

    const detail = await this.getDetailMobileManagement(jwtPayload?.platform);

    this.socketService.versionManagement(jwtPayload?.platform, detail);

    return detail;
  }
}
