import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as ejs from 'ejs';
import * as nodemailer from 'nodemailer';
import { authErrorDetails } from '../domain/messages/error-detail/auth';
import { EnvironmentConfigService } from '../infrastructure/config/environment-config/environment-config.service';
import { EbsServiceApiUrlsConst } from '../utils/contants/ebs-service-api-url.const';
import { sendPost } from '../utils/http';

@Injectable()
export class EmailUsecases {
  private nodemailerTransport: nodemailer.Transporter;

  constructor(
    private config: EnvironmentConfigService,
    private readonly configService: ConfigService,
  ) {
    this.nodemailerTransport = nodemailer.createTransport({
      host: this.config.getMailHost(),
      port: Number(this.config.getMailPort()),
      secure: this.config.getMailSecure(),
      auth:
        this.config.getMailUser() == '<EMAIL>'
          ? {
              type: 'OAuth2',
              user: this.config.getMailUser(),
              clientId: this.config.getAzureADClientId(),
              clientSecret: this.config.getAzureADClientSecret(),
              tenantId: this.config.getAzureADTenantId(),
              refreshToken: this.config.getAzureADRefreshToken(),
              accessUrl: this.config.getAzureADAccessUrl(),
            }
          : {
              user: this.config.getMailUser(),
              pass: this.config.getMailPass(),
            },
    });
  }

  getTransporter() {
    return this.nodemailerTransport;
  }

  async sendEmail(
    to: string,
    subject: string,
    template: string,
    data: object,
    authorization: string,
    cc?: string,
  ) {
    try {
      const mailOptions = {
        from: this.config.getMailUser(),
        to,
        cc: cc ?? this.config.getMailCC(),
        subject: ejs.render(subject, data),
        html: ejs.render(template, data),
      };
      if (this.configService.get<string>('SEND_MAIL_BY_API') === 'true') {
        await sendPost(EbsServiceApiUrlsConst.SEND_MAIL_QUEUE(), mailOptions, {
          authorization,
        });
      } else {
        await this.nodemailerTransport.sendMail(mailOptions);
      }
    } catch (error) {
      throw new HttpException(
        authErrorDetails.E_0105('Unable to send email'),
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}
