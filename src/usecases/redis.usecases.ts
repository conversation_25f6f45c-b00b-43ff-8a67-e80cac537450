import { InjectRedis } from '@nestjs-modules/ioredis';
import { Injectable } from '@nestjs/common';
import { Redis } from 'ioredis';

@Injectable()
export class RedisUsecases {
  constructor(@InjectRedis() private readonly redis: Redis) {}

  async get(key): Promise<any> {
    return await this.redis.get(key);
  }

  async set(key, value, expireTime?: number) {
    if (expireTime) {
      await this.redis.set(key, value, 'EX', expireTime);
    } else {
      await this.redis.set(key, value);
    }
  }

  async remove(key) {
    await this.redis.del(key);
  }
}
