import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableUser1750912071464 implements MigrationInterface {
    name = 'UpdateTableUser1750912071464'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ADD "is_mobile" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "is_mobile"`);
    }

}
