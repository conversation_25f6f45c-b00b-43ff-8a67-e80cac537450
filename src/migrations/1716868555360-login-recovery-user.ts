import { MigrationInterface, QueryRunner } from 'typeorm';

export class LoginRecoveryUser1716868555360 implements MigrationInterface {
  name = 'LoginRecoveryUser1716868555360';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP DEFAULT 'now()', "updated_at" TIMESTAMP DEFAULT 'now()', "deleted_at" TIMESTAMP, "user_name" character varying NOT NULL, "password" character varying NOT NULL, "full_name" character varying NOT NULL, "email" character varying NOT NULL, "phone" character varying NOT NULL, "password_algorithm" character varying NOT NULL DEFAULT 'bcrypt', "salt" character varying NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "is_admin" boolean NOT NULL DEFAULT false, "is_need_otp" boolean NOT NULL DEFAULT false, CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_verify" ("user_id" uuid NOT NULL, "token_type" character varying NOT NULL, "otp" character varying NOT NULL, "token" character varying NOT NULL, "expire_otp_time" bigint NOT NULL, "count_otp_time" bigint NOT NULL DEFAULT '1', "send_otp_time" bigint NOT NULL, CONSTRAINT "PK_9c69bed81c128a2f32927d2aac5" PRIMARY KEY ("user_id", "token_type"))`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "user_verify"`);
    await queryRunner.query(`DROP TABLE "user"`);
  }
}
