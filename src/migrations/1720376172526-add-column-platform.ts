import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnPlatform1720376172526 implements MigrationInterface {
  name = 'AddColumnPlatform1720376172526';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_roles" DROP CONSTRAINT "FK_b23c65e50a758245a33ee35fda1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" DROP CONSTRAINT "FK_87b8888186ca9769c960e926870"`,
    );
    await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "is_admin"`);
    await queryRunner.query(
      `ALTER TABLE "resources" DROP COLUMN "is_internal"`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ADD "platform" character varying DEFAULT 'web_admin'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "platform" character varying DEFAULT 'web_admin'`,
    );
    await queryRunner.query(`ALTER TABLE "user" ADD "employee_id" uuid`);
    await queryRunner.query(`ALTER TABLE "user" ADD "code" character varying`);
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "UQ_c5f78ad8f82e492c25d07f047a5" UNIQUE ("code")`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD CONSTRAINT "FK_87b8888186ca9769c960e926870" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD CONSTRAINT "FK_b23c65e50a758245a33ee35fda1" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_roles" DROP CONSTRAINT "FK_b23c65e50a758245a33ee35fda1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" DROP CONSTRAINT "FK_87b8888186ca9769c960e926870"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "UQ_c5f78ad8f82e492c25d07f047a5"`,
    );
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "code"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "employee_id"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "platform"`);
    await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "platform"`);
    await queryRunner.query(
      `ALTER TABLE "resources" ADD "is_internal" boolean NOT NULL DEFAULT true`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ADD "is_admin" boolean NOT NULL DEFAULT true`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD CONSTRAINT "FK_87b8888186ca9769c960e926870" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD CONSTRAINT "FK_b23c65e50a758245a33ee35fda1" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
