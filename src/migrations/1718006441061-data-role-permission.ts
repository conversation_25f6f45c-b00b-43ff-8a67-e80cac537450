import { MigrationInterface, QueryRunner } from 'typeorm';

export class DataRolePermission1718006441061 implements MigrationInterface {
  name = 'DataRolePermission1718006441061';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_roles" DROP CONSTRAINT "FK_87b8888186ca9769c960e926870"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" DROP CONSTRAINT "FK_b23c65e50a758245a33ee35fda1"`,
    );
    await queryRunner.query(
      `CREATE TABLE "data_permissions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "role_id" uuid NOT NULL, "is_personal" boolean DEFAULT false, "data_role_id" uuid, "allow_view" boolean DEFAULT false, CONSTRAINT "PK_b064fe450245740a4a5bd3a3036" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "name"`);
    await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "path"`);
    await queryRunner.query(
      `ALTER TABLE "data_roles" ADD "description" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ADD "data_role_aliases" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ADD "is_enabled" boolean NOT NULL DEFAULT true`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ALTER COLUMN "created_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ALTER COLUMN "created_at" SET DEFAULT now()`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ALTER COLUMN "updated_at" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ALTER COLUMN "updated_at" SET DEFAULT now()`,
    );
    await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "type"`);
    await queryRunner.query(
      `ALTER TABLE "data_roles" ADD "type" integer NOT NULL DEFAULT '1'`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_permissions" ADD CONSTRAINT "FK_7f2049a0ed35059a98fbf07e318" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_permissions" ADD CONSTRAINT "FK_e4a229a14d06025a931fe3f2f16" FOREIGN KEY ("data_role_id") REFERENCES "data_roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD CONSTRAINT "FK_87b8888186ca9769c960e926870" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD CONSTRAINT "FK_b23c65e50a758245a33ee35fda1" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_roles" DROP CONSTRAINT "FK_b23c65e50a758245a33ee35fda1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" DROP CONSTRAINT "FK_87b8888186ca9769c960e926870"`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_permissions" DROP CONSTRAINT "FK_e4a229a14d06025a931fe3f2f16"`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_permissions" DROP CONSTRAINT "FK_7f2049a0ed35059a98fbf07e318"`,
    );
    await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "type"`);
    await queryRunner.query(
      `ALTER TABLE "data_roles" ADD "type" boolean NOT NULL DEFAULT true`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ALTER COLUMN "updated_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ALTER COLUMN "updated_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ALTER COLUMN "created_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ALTER COLUMN "created_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" ALTER COLUMN "updated_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" ALTER COLUMN "updated_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" ALTER COLUMN "created_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" ALTER COLUMN "created_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "updated_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "updated_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "created_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "created_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ALTER COLUMN "updated_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ALTER COLUMN "updated_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ALTER COLUMN "created_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ALTER COLUMN "created_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" ALTER COLUMN "updated_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" ALTER COLUMN "updated_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" ALTER COLUMN "created_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" ALTER COLUMN "created_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ALTER COLUMN "updated_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ALTER COLUMN "updated_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ALTER COLUMN "created_at" SET DEFAULT '2024-06-10 15:00:15.024405'`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ALTER COLUMN "created_at" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" DROP COLUMN "is_enabled"`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" DROP COLUMN "data_role_aliases"`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" DROP COLUMN "description"`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ADD "path" ltree NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ADD "name" character varying NOT NULL`,
    );
    await queryRunner.query(`DROP TABLE "data_permissions"`);
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD CONSTRAINT "FK_b23c65e50a758245a33ee35fda1" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD CONSTRAINT "FK_87b8888186ca9769c960e926870" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }
}
