import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableAccount1722399271091 implements MigrationInterface {
  name = 'CreateTableAccount1722399271091';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "accounts" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "search_value" character varying, "created_by" character varying, "updated_by" character varying, "deleted_by" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "first_name" character varying, "last_name" character varying, "email" character varying, "staff_id" uuid, "staff_code" character varying, "platform" character varying NOT NULL, "status" character varying DEFAULT 'ACTIVE', "user_id" uuid, CONSTRAINT "PK_5a7a02c20412299d198e097a8fe" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b43bfc475c74889b0e6b64d378" ON "accounts" ("staff_id", "platform") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "employee_id"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "first_name"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "last_name"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "platforms"`);
    await queryRunner.query(
      `ALTER TABLE "resources" ADD "portal" character varying DEFAULT 'web_admin'`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ADD "platform" character varying DEFAULT 'e_purchase'`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" ADD "platform" character varying DEFAULT 'e_purchase'`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ALTER COLUMN "platform" SET DEFAULT 'e_purchase'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "UQ_c5f78ad8f82e492c25d07f047a5"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_af0b1a8359a30dd3f37ee19070" ON "user" ("code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "accounts" ADD CONSTRAINT "FK_3000dad1da61b29953f07476324" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "accounts" DROP CONSTRAINT "FK_3000dad1da61b29953f07476324"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_af0b1a8359a30dd3f37ee19070"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "UQ_c5f78ad8f82e492c25d07f047a5" UNIQUE ("code")`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ALTER COLUMN "platform" SET DEFAULT 'web_admin'`,
    );
    await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "platform"`);
    await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "platform"`);
    await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "portal"`);
    await queryRunner.query(
      `ALTER TABLE "user" ADD "platforms" character varying DEFAULT 'web_internal_portal,web_admin'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "last_name" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD "first_name" character varying`,
    );
    await queryRunner.query(`ALTER TABLE "user" ADD "employee_id" uuid`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b43bfc475c74889b0e6b64d378"`,
    );
    await queryRunner.query(`DROP TABLE "accounts"`);
  }
}
