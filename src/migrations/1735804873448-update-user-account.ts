import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserAccount1735804873448 implements MigrationInterface {
  name = 'UpdateUserAccount1735804873448';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b43bfc475c74889b0e6b64d378"`,
    );
    await queryRunner.query(
      `ALTER TABLE "accounts" ADD "user_name" character varying`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "user_name" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" DROP CONSTRAINT "UQ_d34106f8ec1ebaf66f4f8609dd6"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e12875dfb3b1d92d7d7c5377e2" ON "user" ("email") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3000dad1da61b29953f0747632" ON "accounts" ("user_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5345ad501effcc4b7e415cbcf1" ON "accounts" ("user_name", "platform") WHERE deleted_at IS NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5345ad501effcc4b7e415cbcf1"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_3000dad1da61b29953f0747632"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_e12875dfb3b1d92d7d7c5377e2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD CONSTRAINT "UQ_d34106f8ec1ebaf66f4f8609dd6" UNIQUE ("user_name")`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "user_name" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "user_name"`);
    await queryRunner.query(
      `CREATE INDEX "IDX_b43bfc475c74889b0e6b64d378" ON "accounts" ("staff_id", "platform") WHERE (deleted_at IS NULL)`,
    );
  }
}
