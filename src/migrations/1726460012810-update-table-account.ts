import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTableAccount1726460012810 implements MigrationInterface {
    name = 'UpdateTableAccount1726460012810'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "accounts" ADD "is_admin" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "is_admin"`);
    }

}
