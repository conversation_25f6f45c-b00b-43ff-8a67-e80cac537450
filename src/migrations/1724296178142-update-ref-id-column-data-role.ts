import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateRefIdColumnDataRole1724296178142
  implements MigrationInterface
{
  name = 'UpdateRefIdColumnDataRole1724296178142';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "data_roles" ALTER COLUMN "ref_id" type character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
