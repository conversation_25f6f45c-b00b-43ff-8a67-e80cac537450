import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserNameAccount1735805593609 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`UPDATE accounts
                                SET user_name = "user".user_name
                                FROM "user"
                                WHERE accounts.user_id = "user".id`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
