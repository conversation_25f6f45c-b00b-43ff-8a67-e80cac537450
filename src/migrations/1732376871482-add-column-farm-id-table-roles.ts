import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnFarmIdTableRoles1732376871482 implements MigrationInterface {
    name = 'AddColumnFarmIdTableRoles1732376871482'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "roles" ADD "farm_id" uuid`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "farm_id"`);
    }

}
