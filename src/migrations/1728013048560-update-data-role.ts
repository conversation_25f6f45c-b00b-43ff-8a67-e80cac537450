import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateDataRole1728013048560 implements MigrationInterface {
    name = 'UpdateDataRole1728013048560'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."data_roles_type_enum" RENAME TO "data_roles_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."data_roles_type_enum" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'FUNCTION_UNIT', 'BUSINESS_OWNER', 'MATERIAL', 'MATERIAL_GROUP', 'MATERIAL_TYPE', 'PLANT', 'SUPPLIER', 'PURCHASING_DEPARTMENT', 'PURCHASING_GROUP', 'POSITION', 'CURRENCY_UNIT', 'PR_TYPE', 'PURCHASE_ORDER_TYPE', 'SALE_ORG', 'DIVISION', 'DISTRIBUTION_CHANNEL', 'PURCHASE_REQUEST', 'PURCHASE_ORDER', 'SALE_GROUP')`);
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "type" TYPE "public"."data_roles_type_enum" USING "type"::"text"::"public"."data_roles_type_enum"`);
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "type" SET DEFAULT 'COMPANY'`);
        await queryRunner.query(`DROP TYPE "public"."data_roles_type_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."data_roles_type_enum_old" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'FUNCTION_UNIT', 'BUSINESS_OWNER', 'MATERIAL', 'MATERIAL_GROUP', 'MATERIAL_TYPE', 'PLANT', 'SUPPLIER', 'PURCHASING_DEPARTMENT', 'PURCHASING_GROUP', 'POSITION', 'CURRENCY_UNIT', 'PR_TYPE', 'PURCHASE_ORDER_TYPE', 'SALE_ORG', 'DIVISION', 'DISTRIBUTION_CHANNEL', 'PURCHASE_REQUEST', 'PURCHASE_ORDER')`);
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "type" TYPE "public"."data_roles_type_enum_old" USING "type"::"text"::"public"."data_roles_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "type" SET DEFAULT 'COMPANY'`);
        await queryRunner.query(`DROP TYPE "public"."data_roles_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."data_roles_type_enum_old" RENAME TO "data_roles_type_enum"`);
    }

}
