import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateDataRoleEnum1725951381871 implements MigrationInterface {
  name = 'UpdateDataRoleEnum1725951381871';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // <PERSON><PERSON>a các dòng có giá trị type = 18
    await queryRunner.query(`
        DELETE FROM "data_roles" WHERE "type" = 18
      `);

    // Tạo enum mới
    await queryRunner.query(
      `CREATE TYPE "public"."data_roles_type_enum" AS ENUM('SECTOR', 'COMPANY', 'BUSINESS_UNIT', 'DEPARTMENT', 'FUNCTION_UNIT', 'BUSINESS_OWNER', 'MATERIAL', 'MATERIAL_GROUP', 'MATERIAL_TYPE', 'PLANT', 'SUPPLIER', 'PURCHASING_DEPARTMENT', 'PURCHASING_GROUP', 'POSITION', 'CURRENCY_UNIT', 'PR_TYPE', 'PURCHASE_ORDER_TYPE', 'SALE_ORG', 'DIVISION', 'DISTRIBUTION_CHANNEL', 'PURCHASE_REQUEST', 'PURCHASE_ORDER')`,
    );

    // Tạo một cột tạm với kiểu enum
    await queryRunner.query(`
        ALTER TABLE "data_roles"
        ADD COLUMN "type_temp" "data_roles_type_enum"
    `);

    // Chuyển đổi giá trị cũ sang giá trị enum mới
    await queryRunner.query(`
        UPDATE "data_roles"
        SET "type_temp" = CASE
            WHEN "type" = 1 THEN 'SECTOR'::data_roles_type_enum
            WHEN "type" = 2 THEN 'COMPANY'::data_roles_type_enum
            WHEN "type" = 3 THEN 'BUSINESS_UNIT'::data_roles_type_enum
            WHEN "type" = 4 THEN 'DEPARTMENT'::data_roles_type_enum
            WHEN "type" = 5 THEN 'FUNCTION_UNIT'::data_roles_type_enum
            WHEN "type" = 6 THEN 'BUSINESS_OWNER'::data_roles_type_enum
            WHEN "type" = 7 THEN 'MATERIAL'::data_roles_type_enum
            WHEN "type" = 8 THEN 'MATERIAL_GROUP'::data_roles_type_enum
            WHEN "type" = 9 THEN 'MATERIAL_TYPE'::data_roles_type_enum
            WHEN "type" = 10 THEN 'PLANT'::data_roles_type_enum
            WHEN "type" = 11 THEN 'SUPPLIER'::data_roles_type_enum
            WHEN "type" = 12 THEN 'PURCHASING_DEPARTMENT'::data_roles_type_enum
            WHEN "type" = 13 THEN 'PURCHASING_GROUP'::data_roles_type_enum
            WHEN "type" = 14 THEN 'POSITION'::data_roles_type_enum
            WHEN "type" = 15 THEN 'CURRENCY_UNIT'::data_roles_type_enum
            WHEN "type" = 16 THEN 'PR_TYPE'::data_roles_type_enum
            WHEN "type" = 17 THEN 'PURCHASE_ORDER_TYPE'::data_roles_type_enum
            WHEN "type" = 19 THEN 'PURCHASE_REQUEST'::data_roles_type_enum
            WHEN "type" = 20 THEN 'PURCHASE_ORDER'::data_roles_type_enum
        END
    `);

    // Xoá cột integer ban đầu
    await queryRunner.query(`
        ALTER TABLE "data_roles"
        DROP COLUMN "type"
    `);

    // Đổi tên cột tạm thành cột chính
    await queryRunner.query(`
        ALTER TABLE "data_roles"
        RENAME COLUMN "type_temp" TO "type"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Tạo lại cột integer
    await queryRunner.query(`
        ALTER TABLE "data_roles"
        ADD COLUMN "type_temp" integer
    `);

    // Đổi lại kiểu về integer và cập nhật giá trị về lại số
    await queryRunner.query(`
        UPDATE "data_roles"
        SET "type_temp" = CASE
            WHEN "type" = 'SECTOR' THEN 1
            WHEN "type" = 'COMPANY' THEN 2
            WHEN "type" = 'BUSINESS_UNIT' THEN 3
            WHEN "type" = 'DEPARTMENT' THEN 4
            WHEN "type" = 'FUNCTION_UNIT' THEN 5
            WHEN "type" = 'BUSINESS_OWNER' THEN 6
            WHEN "type" = 'MATERIAL' THEN 7
            WHEN "type" = 'MATERIAL_GROUP' THEN 8
            WHEN "type" = 'MATERIAL_TYPE' THEN 9
            WHEN "type" = 'PLANT' THEN 10
            WHEN "type" = 'SUPPLIER' THEN 11
            WHEN "type" = 'PURCHASING_DEPARTMENT' THEN 12
            WHEN "type" = 'PURCHASING_GROUP' THEN 13
            WHEN "type" = 'POSITION' THEN 14
            WHEN "type" = 'CURRENCY_UNIT' THEN 15
            WHEN "type" = 'PR_TYPE' THEN 16
            WHEN "type" = 'PURCHASE_ORDER_TYPE' THEN 17
            WHEN "type" = 'STAFF_CODE' THEN 18
            WHEN "type" = 'PURCHASE_REQUEST' THEN 19
            WHEN "type" = 'PURCHASE_ORDER' THEN 20
        END
    `);

    // Đổi tên cột tạm thành cột chính
    await queryRunner.query(`
        ALTER TABLE "data_roles"
        RENAME COLUMN "type_temp" TO "type"
      `);

    // Xoá enum
    await queryRunner.query(`
        DROP TYPE "data_roles_type_enum"
      `);
  }
}
