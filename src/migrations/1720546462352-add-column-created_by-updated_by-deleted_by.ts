import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnCreatedByUpdatedByDeletedBy1720546462352 implements MigrationInterface {
    name = 'AddColumnCreatedByUpdatedByDeletedBy1720546462352'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "full_name"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "is_active"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "platform"`);
        await queryRunner.query(`ALTER TABLE "data_roles" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_roles" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_roles" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_permissions" ADD "search_value" character varying`);
        await queryRunner.query(`ALTER TABLE "data_permissions" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_permissions" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_permissions" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resources" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resources" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resources" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "role_permissions" ADD "search_value" character varying`);
        await queryRunner.query(`ALTER TABLE "role_permissions" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "role_permissions" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "role_permissions" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "user" ADD "search_value" character varying`);
        await queryRunner.query(`ALTER TABLE "user" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "user" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "user" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "user" ADD "first_name" character varying`);
        await queryRunner.query(`ALTER TABLE "user" ADD "last_name" character varying`);
        await queryRunner.query(`ALTER TABLE "user" ADD "status" character varying DEFAULT 'ACTIVE'`);
        await queryRunner.query(`ALTER TABLE "user" ADD "platforms" character varying DEFAULT 'web_internal_portal,web_admin'`);
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "resources" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "roles" ALTER COLUMN "search_value" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "user" ADD CONSTRAINT "UQ_d34106f8ec1ebaf66f4f8609dd6" UNIQUE ("user_name")`);
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "password" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "user" ALTER COLUMN "password" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "user" DROP CONSTRAINT "UQ_d34106f8ec1ebaf66f4f8609dd6"`);
        await queryRunner.query(`ALTER TABLE "roles" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "resources" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "search_value" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "platforms"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "status"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "last_name"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "first_name"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "search_value"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP COLUMN "search_value"`);
        await queryRunner.query(`ALTER TABLE "resource_actions" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "resource_actions" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "resource_actions" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "data_permissions" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "data_permissions" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "data_permissions" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "data_permissions" DROP COLUMN "search_value"`);
        await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "platform" character varying DEFAULT 'web_admin'`);
        await queryRunner.query(`ALTER TABLE "user" ADD "is_active" boolean NOT NULL DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "user" ADD "full_name" character varying NOT NULL`);
    }

}
