import { MigrationInterface, QueryRunner } from 'typeorm';

export class RoleInit1717059187406 implements MigrationInterface {
  name = 'RoleInit1717059187406';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "resources" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP DEFAULT 'now()', "updated_at" TIMESTAMP DEFAULT 'now()', "deleted_at" TIMESTAMP, "resource_aliases" character varying NOT NULL, "description" character varying NOT NULL, "parent_id" uuid, "search_value" character varying NOT NULL, "is_enabled" boolean NOT NULL DEFAULT true, "resource_order" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_632484ab9dff41bba94f9b7c85e" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "resource_actions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP DEFAULT 'now()', "updated_at" TIMESTAMP DEFAULT 'now()', "deleted_at" TIMESTAMP, "action_aliases" character varying NOT NULL, "description" character varying NOT NULL, "resource_id" uuid NOT NULL, "resource_id_group" uuid, "search_value" character varying NOT NULL, "is_enabled" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_3fa4c5ccab70888d4dc587138d2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "role_permissions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP DEFAULT 'now()', "updated_at" TIMESTAMP DEFAULT 'now()', "deleted_at" TIMESTAMP, "role_id" uuid NOT NULL, "action_view" boolean DEFAULT false, "action_create" boolean DEFAULT false, "action_edit" boolean DEFAULT false, "action_delete" boolean DEFAULT false, "resource_id" uuid NOT NULL, "resource_action_id" uuid, CONSTRAINT "PK_84059017c90bfcb701b8fa42297" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "data_roles" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP DEFAULT 'now()', "updated_at" TIMESTAMP DEFAULT 'now()', "deleted_at" TIMESTAMP, "name" character varying NOT NULL, "search_value" character varying NOT NULL, "parent_id" uuid, "type" boolean NOT NULL DEFAULT '1', "path" ltree NOT NULL, CONSTRAINT "PK_24de92c1e0b2e59e3c2add742bd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "roles" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP DEFAULT 'now()', "updated_at" TIMESTAMP DEFAULT 'now()', "deleted_at" TIMESTAMP, "name" character varying NOT NULL, "description" character varying NOT NULL, "search_value" character varying NOT NULL, "parent_id" uuid, "allow_inherit" boolean NOT NULL DEFAULT true, "path" ltree, CONSTRAINT "PK_c1433d71a4838793a49dcad46ab" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_data_roles" ("data_role_id" uuid NOT NULL, "user_id" uuid NOT NULL, CONSTRAINT "PK_dd84386c5f0f6cb4eadf8b5d2c5" PRIMARY KEY ("data_role_id", "user_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2dbbbcb67aed7386d0f1dc1029" ON "user_data_roles" ("data_role_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2871a81ac3b7a223af62dd53fb" ON "user_data_roles" ("user_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "user_roles" ("user_id" uuid NOT NULL, "role_id" uuid NOT NULL, CONSTRAINT "PK_23ed6f04fe43066df08379fd034" PRIMARY KEY ("user_id", "role_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_87b8888186ca9769c960e92687" ON "user_roles" ("user_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b23c65e50a758245a33ee35fda" ON "user_roles" ("role_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "created_at" SET DEFAULT 'now()'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "updated_at" SET DEFAULT 'now()'`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" ADD CONSTRAINT "FK_138ac50bdb6c58b1bb53ed3dd98" FOREIGN KEY ("parent_id") REFERENCES "resources"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" ADD CONSTRAINT "FK_8d94004b1e505deee851600d725" FOREIGN KEY ("resource_id") REFERENCES "resources"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" ADD CONSTRAINT "FK_362050a79aaccc42d8cb5cf3dff" FOREIGN KEY ("resource_id_group") REFERENCES "resources"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ADD CONSTRAINT "FK_178199805b901ccd220ab7740ec" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ADD CONSTRAINT "FK_bcc469570df0158006c95ea1118" FOREIGN KEY ("resource_id") REFERENCES "resources"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" ADD CONSTRAINT "FK_1ec65950932ed7514f2bff10f5a" FOREIGN KEY ("resource_action_id") REFERENCES "resource_actions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" ADD CONSTRAINT "FK_20df22b424e5c241a23079a443b" FOREIGN KEY ("parent_id") REFERENCES "data_roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" ADD CONSTRAINT "FK_3e97eeaf865aeda0d20c0c5c509" FOREIGN KEY ("parent_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_data_roles" ADD CONSTRAINT "FK_2dbbbcb67aed7386d0f1dc10297" FOREIGN KEY ("data_role_id") REFERENCES "data_roles"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_data_roles" ADD CONSTRAINT "FK_2871a81ac3b7a223af62dd53fb2" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD CONSTRAINT "FK_87b8888186ca9769c960e926870" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" ADD CONSTRAINT "FK_b23c65e50a758245a33ee35fda1" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "user_roles" DROP CONSTRAINT "FK_b23c65e50a758245a33ee35fda1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_roles" DROP CONSTRAINT "FK_87b8888186ca9769c960e926870"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_data_roles" DROP CONSTRAINT "FK_2871a81ac3b7a223af62dd53fb2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_data_roles" DROP CONSTRAINT "FK_2dbbbcb67aed7386d0f1dc10297"`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles" DROP CONSTRAINT "FK_3e97eeaf865aeda0d20c0c5c509"`,
    );
    await queryRunner.query(
      `ALTER TABLE "data_roles" DROP CONSTRAINT "FK_20df22b424e5c241a23079a443b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" DROP CONSTRAINT "FK_1ec65950932ed7514f2bff10f5a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" DROP CONSTRAINT "FK_bcc469570df0158006c95ea1118"`,
    );
    await queryRunner.query(
      `ALTER TABLE "role_permissions" DROP CONSTRAINT "FK_178199805b901ccd220ab7740ec"`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" DROP CONSTRAINT "FK_362050a79aaccc42d8cb5cf3dff"`,
    );
    await queryRunner.query(
      `ALTER TABLE "resource_actions" DROP CONSTRAINT "FK_8d94004b1e505deee851600d725"`,
    );
    await queryRunner.query(
      `ALTER TABLE "resources" DROP CONSTRAINT "FK_138ac50bdb6c58b1bb53ed3dd98"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "updated_at" SET DEFAULT '2024-05-30 15:46:00.952497'`,
    );
    await queryRunner.query(
      `ALTER TABLE "user" ALTER COLUMN "created_at" SET DEFAULT '2024-05-30 15:46:00.952497'`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b23c65e50a758245a33ee35fda"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_87b8888186ca9769c960e92687"`,
    );
    await queryRunner.query(`DROP TABLE "user_roles"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2871a81ac3b7a223af62dd53fb"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_2dbbbcb67aed7386d0f1dc1029"`,
    );
    await queryRunner.query(`DROP TABLE "user_data_roles"`);
    await queryRunner.query(`DROP TABLE "roles"`);
    await queryRunner.query(`DROP TABLE "data_roles"`);
    await queryRunner.query(`DROP TABLE "role_permissions"`);
    await queryRunner.query(`DROP TABLE "resource_actions"`);
    await queryRunner.query(`DROP TABLE "resources"`);
  }
}
