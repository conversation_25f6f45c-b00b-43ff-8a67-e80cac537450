import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateRoleTable1726653961852 implements MigrationInterface {
    name = 'UpdateRoleTable1726653961852'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_397436cc6a5eb604cb70df0d4b"`);
        await queryRunner.query(`CREATE INDEX "IDX_df0cb901df5b430904ad10b619" ON "roles" ("code", "platform") WHERE deleted_at IS NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_df0cb901df5b430904ad10b619"`);
        await queryRunner.query(`CREATE INDEX "IDX_397436cc6a5eb604cb70df0d4b" ON "roles" ("code") WHERE (deleted_at IS NULL)`);
    }

}
