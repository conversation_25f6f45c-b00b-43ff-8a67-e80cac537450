import { MigrationInterface, QueryRunner } from "typeorm";

export class UpadteNewDataRole1725954660284 implements MigrationInterface {
    name = 'UpadteNewDataRole1725954660284'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "type" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "type" SET DEFAULT 'COMPANY'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "type" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "data_roles" ALTER COLUMN "type" DROP NOT NULL`);
    }

}
