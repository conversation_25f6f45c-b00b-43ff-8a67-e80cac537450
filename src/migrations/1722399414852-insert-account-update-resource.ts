import { MigrationInterface, QueryRunner } from 'typeorm';

export class InsertAccountUpdateResource1722399414852
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`INSERT INTO accounts (id, platform, user_id, created_at, updated_at)
        SELECT
            uuid_generate_v4 () AS id,
            'e_purchase' AS platform,
            v.id AS user_id,
            now(),
            now()
        FROM (
            SELECT
                u.id
            FROM
                "user" u
            WHERE
                u.deleted_at IS NULL) AS v`);
    await queryRunner.query(`UPDATE
    resources
    SET
    portal = CASE WHEN platform = 'web_admin' THEN
     'web_admin'
    ELSE
     'web_internal_portal'
    END,
    platform = 'e_purchase'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}
