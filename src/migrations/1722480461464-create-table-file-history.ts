import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableFileHistory1722480461464 implements MigrationInterface {
    name = 'CreateTableFileHistory1722480461464'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."identity-file-import-histories_status_enum" AS ENUM('SUCCESS', 'FAIL', 'IN_PROCESS', 'WAITING')`);
        await queryRunner.query(`CREATE TYPE "public"."identity-file-import-histories_import_type_enum" AS ENUM('USER')`);
        await queryRunner.query(`CREATE TABLE "identity-file-import-histories" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "search_value" character varying, "created_by" jsonb, "updated_by" jsonb, "deleted_by" jsonb, "file_name" character varying NOT NULL, "file_path" character varying NOT NULL, "status" "public"."identity-file-import-histories_status_enum" DEFAULT 'WAITING', "import_type" "public"."identity-file-import-histories_import_type_enum" NOT NULL, "errors" character varying, CONSTRAINT "PK_93fae1ceed1c864f94de7ec6265" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "data_roles" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "data_roles" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "data_roles" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "data_permissions" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "data_permissions" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "data_permissions" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "data_permissions" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "data_permissions" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "data_permissions" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "resources" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "resources" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "resources" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "resource_actions" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "resource_actions" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "resource_actions" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "deleted_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "created_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "updated_by" jsonb`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "deleted_by" jsonb`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "accounts" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "accounts" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "user" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "roles" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "roles" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "role_permissions" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "role_permissions" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resource_actions" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resource_actions" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resource_actions" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "resource_actions" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "resources" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "resources" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "resources" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "resources" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_permissions" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "data_permissions" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_permissions" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "data_permissions" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_permissions" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "data_permissions" ADD "created_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "deleted_by"`);
        await queryRunner.query(`ALTER TABLE "data_roles" ADD "deleted_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "data_roles" ADD "updated_by" character varying`);
        await queryRunner.query(`ALTER TABLE "data_roles" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "data_roles" ADD "created_by" character varying`);
        await queryRunner.query(`DROP TABLE "identity-file-import-histories"`);
        await queryRunner.query(`DROP TYPE "public"."identity-file-import-histories_import_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."identity-file-import-histories_status_enum"`);
    }

}
