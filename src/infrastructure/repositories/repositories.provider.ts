import { Provider } from '@nestjs/common';
import { IAccountRepository } from '../../domain/repositories/account.repository';
import { IDataPermissionRepository } from '../../domain/repositories/data-permission.repository';
import { IDataRoleRepository } from '../../domain/repositories/data-role.repository';
import { IFileImportHistoryRepository } from '../../domain/repositories/file-import-history.repository';
import { ILoginLogRepository } from '../../domain/repositories/login-log.repository';
import { IResourceActionRepository } from '../../domain/repositories/resource-action.repository';
import { IResourceRepository } from '../../domain/repositories/resource.repository';
import { IRolePermissionRepository } from '../../domain/repositories/role-permission.repository';
import { IRoleRepository } from '../../domain/repositories/role.repository';
import { IUserRepository } from '../../domain/repositories/user.repository';
import { IUserVerifyRepository } from '../../domain/repositories/userVerifyRepository.interface';
import { AccountRepository } from './account.repository';
import { DataPermissionRepository } from './data-permission.repository';
import { DataRoleRepository } from './data-role.repository';
import { FileImportHistoryRepository } from './file-import-history.repository';
import { LoginLogRepository } from './login-log.repository';
import { ResourceActionRepository } from './resource-action.repository';
import { ResourceRepository } from './resource.repository';
import { RolePermissionRepository } from './role-permission.repository';
import { RoleRepository } from './role.repository';
import { UserVerifyRepository } from './user-verify.repository';
import { UserRepository } from './user.repository';
import { IApiLogRepository } from '../../domain/repositories/api-log.repository';
import { ApiLogRepository } from './api-log.repository';
import { IMobileManagementRepository } from '../../domain/repositories/mobile-management.repository';
import { MobileManagementRepository } from './mobile-management.repository';

export const UserRepositoryProvider = {
  provide: IUserRepository,
  useClass: UserRepository,
};

export const UserVerifyRepositoryProvider = {
  provide: IUserVerifyRepository,
  useClass: UserVerifyRepository,
};

export const RoleRepositoryProvider = {
  provide: IRoleRepository,
  useClass: RoleRepository,
};

export const ResourceRepositoryProvider = {
  provide: IResourceRepository,
  useClass: ResourceRepository,
};

export const RolePermissionRepositoryProvider = {
  provide: IRolePermissionRepository,
  useClass: RolePermissionRepository,
};

export const DataRoleRepositoryProvider = {
  provide: IDataRoleRepository,
  useClass: DataRoleRepository,
};

export const DataPermissionRepositoryProvider = {
  provide: IDataPermissionRepository,
  useClass: DataPermissionRepository,
};

export const ResourceActionRepositoryProvider = {
  provide: IResourceActionRepository,
  useClass: ResourceActionRepository,
};

export const AccountRepositoryProvider = {
  provide: IAccountRepository,
  useClass: AccountRepository,
};

export const FileImportHistoryRepositoryProvider = {
  provide: IFileImportHistoryRepository,
  useClass: FileImportHistoryRepository,
};

export const LoginLogRepositoryProvider = {
  provide: ILoginLogRepository,
  useClass: LoginLogRepository,
};

export const ApiLogRepositoryProvider = {
  provide: IApiLogRepository,
  useClass: ApiLogRepository,
};

export const MobileManagementRepositoryProvider = {
  provide: IMobileManagementRepository,
  useClass: MobileManagementRepository,
};

export const RepositoryProviders: Provider[] = [
  UserRepositoryProvider,
  UserVerifyRepositoryProvider,
  RoleRepositoryProvider,
  ResourceRepositoryProvider,
  RolePermissionRepositoryProvider,
  DataRoleRepositoryProvider,
  DataPermissionRepositoryProvider,
  ResourceActionRepositoryProvider,
  AccountRepositoryProvider,
  FileImportHistoryRepositoryProvider,
  LoginLogRepositoryProvider,
  ApiLogRepositoryProvider,
  MobileManagementRepositoryProvider,
];
