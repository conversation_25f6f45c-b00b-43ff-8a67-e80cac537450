import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { IUpdateUserVerify } from '../../domain/interface/user-verify.interface';
import { UserVerifyModel } from '../../domain/model/user-verify.model';
import { IUserVerifyRepository } from '../../domain/repositories/userVerifyRepository.interface';
import { UserVerifyEntity } from '../entities/user-verify.entity';
import { BaseRepository } from './base.repository';

@Injectable({ scope: Scope.REQUEST })
export class UserVerifyRepository
  extends BaseRepository
  implements IUserVerifyRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async getUserVerify(
    userId: string,
    tokenType: string,
  ): Promise<UserVerifyModel> {
    const repository = this.getRepository(UserVerifyEntity);

    return await repository.findOne({
      where: {
        userId: userId,
        tokenType: tokenType,
      },
    });
  }
  async updateUserVerify(userId: string, userVerify: IUpdateUserVerify) {
    const repository = this.getRepository(UserVerifyEntity);

    await repository.update(
      {
        userId: userId,
        tokenType: userVerify.tokenType,
      },
      {
        otp: userVerify.otp,
        token: userVerify.token,
        countOtpTime: userVerify.countOtpTime,
        expireOtpTime: userVerify.expireOtpTime,
        sendOtpTime: userVerify.sendOtpTime,
      },
    );
  }
  async createUserVerify(
    userVerify: UserVerifyModel,
  ): Promise<UserVerifyModel> {
    const repository = this.getRepository(UserVerifyEntity);

    return await repository.save({
      userId: userVerify.userId,
      otp: userVerify.otp,
      token: userVerify.token,
      expireOtpTime: userVerify.expireOtpTime,
      countOtpTime: userVerify.countOtpTime,
      sendOtpTime: userVerify.sendOtpTime,
      tokenType: userVerify.tokenType,
    });
  }
  async deleteUserVerify(userId: string, tokenType: string) {
    const repository = this.getRepository(UserVerifyEntity);

    await repository.delete({ userId: userId, tokenType: tokenType });
  }
}
