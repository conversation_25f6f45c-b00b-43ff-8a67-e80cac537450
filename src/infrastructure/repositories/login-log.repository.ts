import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GetListLoginLogDto } from '../../controller/auth/dtos/get-list-login-log.dto';
import { LoginLogDto } from '../../controller/auth/dtos/login-log.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { LoginLogModel } from '../../domain/model/login-log.model';
import { ILoginLogRepository } from '../../domain/repositories/login-log.repository';
import { LoginLogEntity } from '../schemas/login-log.schema';
import { BaseMongoRepository } from './base-mongo.repository';

@Injectable()
export class LoginLogRepository
  extends BaseMongoRepository<LoginLogEntity>
  implements ILoginLogRepository
{
  constructor(
    @InjectModel(LoginLogEntity.name)
    protected model: Model<LoginLogEntity>,
  ) {
    super(model);
  }

  async create(data: LoginLogDto): Promise<LoginLogModel> {
    const doc = await this.model.create(data);
    const model = doc.toObject();
    return model;
  }

  async getList(
    conditions: GetListLoginLogDto,
  ): Promise<ResponseDto<LoginLogModel>> {
    const query = this.buildFindQuery(conditions);
    return await this.pagination(conditions, query);
  }

  private buildFindQuery(conditions: GetListLoginLogDto): any {
    const query: any = {};

    query.platform = { $eq: conditions.platform };

    query.portal = { $in: conditions.portals };

    return query;
  }
}
