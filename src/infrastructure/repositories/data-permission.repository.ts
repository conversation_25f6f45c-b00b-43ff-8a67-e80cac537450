import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource } from 'typeorm';
import { DataPermissionModel } from '../../domain/model/data-permission.model';
import { IDataPermissionRepository } from '../../domain/repositories/data-permission.repository';
import { DataPermissionEntity } from '../entities/data-permission.entity';
import { BaseRepository } from './base.repository';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class DataPermissionRepository
  extends BaseRepository
  implements IDataPermissionRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createDataPermissions(
    models: DataPermissionModel[],
  ): Promise<DataPermissionModel[]> {
    const repository = this.getRepository(DataPermissionEntity);

    const entities: DataPermissionEntity[] = [];
    for (const model of models) {
      const dataPermission = repository.create({
        roleId: model.roleId,
        dataRoleId: model.dataRoleId,
        isPersonal: model.isPersonal,
        allowView: model.allowView,
      });

      entities.push(dataPermission);
    }

    if (entities.length) {
      return await repository.save(entities);
    }

    return [];
  }
  updateDataPermission(
    data: DataPermissionModel,
  ): Promise<DataPermissionModel> {
    return;
  }

  async deleteDataPermissionByRoleId(roleId: string): Promise<void> {
    const repository = this.getRepository(DataPermissionEntity);
    await repository.delete({ roleId });
  }

  async getDataPermissionById(id: string): Promise<DataPermissionModel> {
    return;
  }
  async getDataPermissions(): Promise<DataPermissionModel[]> {
    return;
  }
}
