import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, In } from 'typeorm';
import { GetApproverByPositionDto } from '../../controller/user/dtos/get-approver-by-position.dto';
import { GetUserListDto } from '../../controller/user/dtos/get-user-list.dto';
import { DataRoleType } from '../../domain/config/enums/data-role-type.enum';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { EStatus } from '../../domain/config/enums/status.enum';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { AccountModel } from '../../domain/model/account.model';
import { UserModel } from '../../domain/model/user.model';
import { IUserRepository } from '../../domain/repositories/user.repository';
import { removeUnicode } from '../../utils/common';
import { AccountEntity } from '../entities/account.entity';
import { UserEntity } from '../entities/user.entity';
import { BaseRepository } from './base.repository';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class UserRepository extends BaseRepository implements IUserRepository {
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createUser(data: UserModel) {
    const repository = this.getRepository(UserEntity);

    const newUser = repository.create(data);

    return await repository.save(newUser);
  }
  async getUserByUserName(
    username: string,
    platform?: EPlatform,
  ): Promise<UserModel> {
    const repository = this.getRepository(UserEntity);

    const queryBuilder = repository.createQueryBuilder('user');

    queryBuilder.addSelect('user.password');

    queryBuilder.innerJoinAndSelect('user.accounts', 'accounts');
    queryBuilder.leftJoinAndSelect('user.roles', 'roles');

    queryBuilder.andWhere(
      ' (accounts.userName = :username OR user.email = :username) AND accounts.status = :status',
      {
        username: username,
        status: EStatus.ACTIVE,
      },
    );

    queryBuilder.andWhere('accounts.platform = :platform', {
      platform: platform || EPlatform.E_PURCHASE,
    });

    return await queryBuilder.getOne();
  }

  async checkDuplicateUsername(username: string): Promise<UserModel> {
    const repository = this.getRepository(UserEntity);

    const userExists = await repository
      .createQueryBuilder('user')
      .where('user.userName = :username', {
        username,
      })
      .getOne();

    return userExists; // Trả về true nếu user tồn tại, ngược lại false
  }

  async getByIdAndPassword(id: string, encodePass: string): Promise<UserModel> {
    const repository = this.getRepository(UserEntity);
    return await repository.findOne({
      where: {
        id: id,
        password: encodePass,
      },
    });
  }
  async getUserInfo(
    id: string,
    platform?: EPlatform,
    selectPassword?: boolean,
    getAllPlatform: boolean = false,
  ): Promise<UserModel> {
    const repository = this.getRepository(UserEntity);

    const queryBuilder = repository.createQueryBuilder('user');

    if (selectPassword) {
      queryBuilder.addSelect('user.password');
    }

    if (getAllPlatform) {
      queryBuilder
        .leftJoinAndSelect('user.roles', 'roles')
        .leftJoinAndSelect('user.accounts', 'accounts');
    } else {
      queryBuilder
        .leftJoinAndMapMany(
          'user.roles',
          'user.roles',
          'roles',
          'roles.platform = :platform',
          { platform },
        )
        .leftJoinAndMapMany(
          'user.accounts',
          'user.accounts',
          'accounts',
          'accounts.platform = :platform',
          { platform },
        );
    }

    queryBuilder.andWhere('user.id = :id', {
      id: id,
    });

    return await queryBuilder.getOne();
  }

  async getUserByEmail(
    email: string,
    id?: string,
    platform?: EPlatform,
  ): Promise<UserModel> {
    const repository = this.getRepository(UserEntity);

    const queryBuilder = repository.createQueryBuilder('user');

    queryBuilder.innerJoinAndSelect('user.accounts', 'accounts');
    queryBuilder.leftJoinAndSelect('user.roles', 'roles');

    queryBuilder.andWhere(
      ' user.email = :email AND accounts.status = :status',
      {
        email: email,
        status: EStatus.ACTIVE,
      },
    );

    if (platform) {
      queryBuilder.andWhere('accounts.platform = :platform', {
        platform: platform || EPlatform.E_PURCHASE,
      });
    }

    if (id) {
      queryBuilder.andWhere('user.id != :id', {
        id: id,
      });
    }

    return await queryBuilder.getOne();
  }

  async updateUserPassword(userId: string, password: string): Promise<void> {
    const repository = this.getRepository(UserEntity);
    const updateUser = repository.create({
      id: userId,
      password: password,
    });
    await repository.save(updateUser);
  }

  async countAssignedUserToRole(roleId: string): Promise<number> {
    const repository = this.getRepository(UserEntity);
    return await repository.count({
      where: {
        roles: {
          id: roleId,
        },
      },
    });
  }

  async deleteUser(userId: string): Promise<void> {
    const repository = this.getRepository(UserEntity);

    await repository.softDelete({ id: userId });
  }

  async getUsers(
    conditions: GetUserListDto,
    platform?: EPlatform,
  ): Promise<ResponseDto<UserModel>> {
    const repository = this.getRepository(UserEntity);

    const queryBuilder = repository.createQueryBuilder('users');
    queryBuilder
      // .leftJoinAndSelect('users.roles', 'roles')
      .leftJoinAndMapMany(
        'users.roles',
        'users.roles',
        'roles',
        'roles.platform = :platform',
        { platform },
      )
      .leftJoinAndMapMany(
        'users.accounts',
        'users.accounts',
        'accounts',
        'accounts.platform = :platform',
        { platform },
      );

    if (conditions.statuses && conditions.statuses.length) {
      queryBuilder.andWhere('users.status IN (:...statuses)', {
        statuses: conditions.statuses,
      });
    }

    if (conditions?.roleIds?.length) {
      queryBuilder.andWhere('roles.id IN (:...roleIds)', {
        roleIds: conditions.roleIds,
      });
    }

    if (conditions.searchString) {
      const originSearch = `%${conditions.searchString}%`;
      const transformSearch = `%${removeUnicode(conditions.searchString)}%`;

      queryBuilder.andWhere(
        '(users.search_value ILIKE :transformSearch OR users.code ILIKE :searchString OR users.phone ILIKE :searchString OR remove_unicode(accounts.user_name) ILIKE :transformSearch)',
        {
          searchString: originSearch,
          transformSearch: transformSearch,
        },
      );
    }

    if (conditions.staffIds?.length) {
      queryBuilder.andWhere('accounts.staffId IN (:...staffIds)', {
        staffIds: conditions.staffIds,
      });
    }

    queryBuilder.andWhere('accounts.platform = :platform', {
      platform: platform || EPlatform.E_PURCHASE,
    });

    queryBuilder.orderBy('users.createdAt', 'DESC');

    return await this.pagination(queryBuilder, {
      ...conditions,
      searchString: null,
    });
  }

  async updateUser(user: UserModel): Promise<UserModel> {
    const repository = this.getRepository(UserEntity);

    const updateUser = repository.create({
      ...user,
    });

    return await repository.save(updateUser);
  }

  async getUserByEmails(emails: string[]): Promise<UserModel[]> {
    const repository = this.getRepository(UserEntity);

    return await repository.find({
      where: {
        email: In(emails),
      },
      relations: ['accounts'],
    });
  }

  async getUserByUserNames(names: string[]): Promise<AccountModel[]> {
    const repository = this.getRepository(AccountEntity);

    return await repository.find({
      where: {
        userName: In(names),
      },
      relations: ['user'],
    });
  }

  async approverByPosition(
    conditions: GetApproverByPositionDto,
  ): Promise<AccountModel[]> {
    const repository = this.getRepository(AccountEntity);

    const queryBuilder = repository
      .createQueryBuilder('accounts')
      .select([
        'accounts.id',
        'accounts.firstName',
        'accounts.lastName',
        'accounts.email',
        'accounts.staffId',
        'accounts.staffCode',
      ]);

    queryBuilder.andWhere('accounts.platform = :platform', {
      platform: conditions.platform || EPlatform.E_PURCHASE,
    });

    queryBuilder
      .innerJoin('accounts.user', 'user')
      .addSelect(['user.id', 'user.userName']);
    queryBuilder
      .innerJoin('user.roles', 'roles', 'roles.platform = :platform', {
        platform: conditions.platform || EPlatform.E_PURCHASE,
      })
      .addSelect(['roles.id', 'roles.name']);
    queryBuilder
      .innerJoin('roles.dataPermissions', 'dataPermissions')
      .addSelect(['dataPermissions.id']);
    queryBuilder
      .innerJoin(
        'dataPermissions.dataRole',
        'dataRole',
        '(dataRole.refId IN (:...businessUnitIds) OR dataRole.refId IN (:...departmentIds) OR dataRole.refId IN (:...functionUnitIds)) AND dataRole.type IN (:...types)',
        {
          businessUnitIds: conditions.businessUnitIds?.length
            ? conditions.businessUnitIds
            : [null],
          departmentIds: conditions.departmentIds?.length
            ? conditions.departmentIds
            : [null],
          functionUnitIds: conditions.functionUnitIds?.length
            ? conditions.functionUnitIds
            : [null],
          types: [
            DataRoleType.BUSINESS_UNIT,
            DataRoleType.DEPARTMENT,
            DataRoleType.FUNCTION_UNIT,
          ],
        },
      )
      .addSelect(['dataRole.id', 'dataRole.refId', 'dataRole.type']);

    queryBuilder.andWhere('accounts.staffId IS NOT NULL');

    return queryBuilder.getMany();
  }

  async getUserInfoMobile(
    id: string,
    platform?: EPlatform,
    selectPassword?: boolean,
    getAllPlatform: boolean = false,
  ): Promise<UserModel> {
    const repository = this.getRepository(UserEntity);

    const queryBuilder = repository.createQueryBuilder('user');

    queryBuilder.andWhere('user.isMobile = :isMobile', {
      isMobile: true,
    });

    if (selectPassword) {
      queryBuilder.addSelect('user.password');
    }

    if (getAllPlatform) {
      queryBuilder
        .leftJoinAndSelect('user.roles', 'roles')
        .leftJoinAndSelect('user.accounts', 'accounts');
    } else {
      queryBuilder
        .leftJoinAndMapMany(
          'user.roles',
          'user.roles',
          'roles',
          'roles.platform = :platform',
          { platform },
        )
        .leftJoinAndMapMany(
          'user.accounts',
          'user.accounts',
          'accounts',
          'accounts.platform = :platform',
          { platform },
        );
    }

    queryBuilder.andWhere('user.id = :id', {
      id: id,
    });

    return await queryBuilder.getOne();
  }
}
