import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, FindOptionsWhere, In } from 'typeorm';
import { GetResourceListDto } from '../../controller/resource/dtos/get-resource-list.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { ResourceModel } from '../../domain/model/resource.model';
import { IResourceRepository } from '../../domain/repositories/resource.repository';
import { ResourceEntity } from '../entities/resource.entity';
import { BaseRepository } from './base.repository';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { removeUnicode } from '../../utils/common';
import { EPortal } from '../../domain/config/enums/portal.enum';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class ResourceRepository
  extends BaseRepository
  implements IResourceRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createResource(data: ResourceModel): Promise<ResourceModel> {
    const repository = this.getRepository(ResourceEntity);

    const newResource = repository.create({
      resourceAliases: data.resourceAliases,
      description: data.description,
      parentId: data.parentId,
      resourceOrder: data.resourceOrder,
      platform: data.platform,
    });

    return await repository.save(newResource);
  }
  //@TODO: Update Resource
  async updateResource(data: ResourceModel): Promise<ResourceModel> {
    return null;
  }
  async deleteResource(id: string): Promise<void> {
    const repository = this.getRepository(ResourceEntity);
    await repository.delete(id);
  }
  async getResourceById(id: string): Promise<ResourceModel> {
    const repository = this.getRepository(ResourceEntity);
    return await repository.findOneBy({ id: id });
  }
  async getResources(conditions: GetResourceListDto): Promise<ResourceModel[]> {
    const repository = this.getRepository(ResourceEntity);
    const queryBuilder = repository.createQueryBuilder('resources');

    // const selects = [
    //   'resources.id',
    //   'resources.resourceAliases',
    //   'resources.description',
    //   'resources.parentId',
    //   'resources.resourceOrder',
    //   'resources.isEnabled',
    //   'resources.isAdmin',
    //   'resources.isInternal',
    // ];

    // queryBuilder.select(selects);

    queryBuilder.leftJoinAndSelect(
      'resources.resourceActions',
      'actions',
      'actions.resource_id_group = resources.id',
    );

    queryBuilder.where('parent_id IS NULL');

    if (conditions.portals && conditions.portals.length) {
      queryBuilder.andWhere({ portal: In(conditions.portals) });
    }

    if (conditions.platform && conditions.platform.length) {
      queryBuilder.andWhere({ platform: conditions.platform });
    }

    if (conditions.searchString) {
      queryBuilder.andWhere('resources.search_value ilike :searchString', {
        searchString: `%${removeUnicode(conditions.searchString)}%`,
      });
    }

    return await queryBuilder.getMany();
  }

  async getMaxResourceOrder(parentId: string): Promise<number> {
    const repository = this.getRepository(ResourceEntity);

    const query: FindOptionsWhere<ResourceEntity> = {};

    if (parentId) {
      query.parentId = parentId;
    }
    return (
      (
        await repository
          .createQueryBuilder('resource')
          .select('MAX(resource_order)', 'maxResourceOrder')
          .where(query)
          .getRawOne()
      )?.maxResourceOrder || 0
    );
  }

  async getResourceByRoleId(
    roleId: string,
    platform: EPlatform,
    portal: EPortal,
  ) {
    const repository = this.getRepository(ResourceEntity);
    const result = await repository.query(
      `
          WITH cte_role as (SELECT id AS "roleId", allow_inherit AS "allowInherit", nlevel(path) AS "nlevel", code
                            FROM roles rlb
                            WHERE path @> (SELECT rl."path"
                                            FROM roles rl
                                            WHERE rl."path" ~ $1) AND path != '' AND rlb.deleted_at IS NULL
          ORDER BY nlevel(path) DESC
              )
          SELECT cte_role.*,
                 rp.action_view AS "actionView",
                 rp.action_create AS "actionCreate",
                 rp.action_edit AS "actionEdit",
                 rp.action_delete AS "actionDelete",
                 rp.resource_action_id AS "resourceActionId",
                 r.id AS "resourceId",
                 r.resource_aliases AS "resourceAliases",
                 r.resource_order AS "resourceOrder",
                 r.is_enabled AS "isEnabled",
                 r.parent_id AS "parentId",
                 r.platform AS "platform"
          FROM cte_role
          LEFT JOIN role_permissions rp ON cte_role."roleId" = rp.role_id AND rp.deleted_at IS NULL
          LEFT JOIN resources r ON r.id = rp.resource_id AND r.deleted_at IS NULL
          WHERE r.platform = $2 AND r.portal = $3
          ORDER BY cte_role.nlevel, r.resource_order DESC`,
      ['*.' + roleId.replace(/-/g, ''), platform, portal],
    );

    return result;
  }
}
