import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource } from 'typeorm';
import { RolePermissionModel } from '../../domain/model/role-permission.model';
import { IRolePermissionRepository } from '../../domain/repositories/role-permission.repository';
import { RolePermissionEntity } from '../entities/role-permission.entity';
import { BaseRepository } from './base.repository';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class RolePermissionRepository
  extends BaseRepository
  implements IRolePermissionRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createRolePermissions(
    models: RolePermissionModel[],
  ): Promise<RolePermissionModel[]> {
    const repository = this.getRepository(RolePermissionEntity);

    const entities: RolePermissionEntity[] = [];
    for (const model of models) {
      const rolePermission = repository.create({
        roleId: model.roleId,
        resourceId: model.resourceId,
        resourceActionId: model.resourceActionId,
        actionView: model.actionView,
        actionCreate: model.actionCreate,
        actionEdit: model.actionEdit,
        actionDelete: model.actionDelete,
      });

      entities.push(rolePermission);
    }

    if (entities.length) {
      return await repository.save(entities);
    }

    return [];
  }
  async updateRolePermission(
    data: RolePermissionModel,
  ): Promise<RolePermissionModel> {
    return null;
  }
  async deleteRolePermissionByRoleId(roleId: string): Promise<void> {
    const repository = this.getRepository(RolePermissionEntity);
    await repository.delete({ roleId });
  }
  async getRolePermissionById(id: string): Promise<RolePermissionModel> {
    return null;
  }
  async getRolePermissions(): Promise<RolePermissionModel[]> {
    return null;
  }
}
