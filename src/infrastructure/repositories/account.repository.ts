import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, FindOptionsWhere } from 'typeorm';
import { GetAccountDto } from '../../controller/account/dtos/get-account.dto';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { AccountModel } from '../../domain/model/account.model';
import { IAccountRepository } from '../../domain/repositories/account.repository';
import { AccountEntity } from '../entities/account.entity';
import { BaseRepository } from './base.repository';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class AccountRepository
  extends BaseRepository
  implements IAccountRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createAccount(data: AccountModel): Promise<AccountModel> {
    const repository = this.getRepository(AccountEntity);

    const accountModel = repository.create(data);

    return await repository.save(accountModel);
  }

  async getAccountsByStaffId(
    conditions: GetAccountDto,
  ): Promise<AccountModel[]> {
    const repository = this.getRepository(AccountEntity);

    const queryBuilder = repository.createQueryBuilder('account');

    if (conditions.staffId) {
      queryBuilder.andWhere('account.staffId = :staffId', {
        staffId: conditions.staffId,
      });
    }

    if (conditions.userId) {
      queryBuilder.innerJoin('account.user', 'user');
      queryBuilder.andWhere('user.id = :userId', {
        userId: conditions.userId,
      });
    }

    if (conditions.email) {
      queryBuilder.andWhere('account.email = :email', {
        email: conditions.email,
      });
    }

    if (conditions.staffCode) {
      queryBuilder.andWhere('account.staffCode = :staffCode', {
        staffCode: conditions.staffCode,
      });
    }

    if (conditions.platform) {
      queryBuilder.andWhere('account.platform = :platform', {
        platform: conditions.platform,
      });
    }

    return await queryBuilder.getMany();
  }
  async updateAccount(accountModel: AccountModel): Promise<AccountModel> {
    const repository = this.getRepository(AccountEntity);

    const account = repository.create(accountModel);

    return await repository.save(account);
  }

  async deleteAccountByUserId(
    userId: string,
    platform?: EPlatform,
  ): Promise<void> {
    const repository = this.getRepository(AccountEntity);

    const findOptionsWhere: FindOptionsWhere<AccountEntity> = {
      user: { id: userId },
    };

    if (platform) {
      findOptionsWhere.platform = platform;
    }

    await repository.softDelete(findOptionsWhere);
  }

  async deleteAccountByStaffId(staffId: string, userId: string): Promise<void> {
    const repository = this.getRepository(AccountEntity);

    const findOptionsWhere: FindOptionsWhere<AccountEntity> = {};

    if (staffId) {
      findOptionsWhere.staffId = staffId;
    }

    if (userId) {
      findOptionsWhere.user = { id: userId };
    }

    if (staffId || userId) {
      await repository.softDelete(findOptionsWhere);
    }
  }

  async getAccountByUserName(
    userName: string,
    platform?: EPlatform,
  ): Promise<AccountModel> {
    const repository = this.getRepository(AccountEntity);

    return await repository.findOne({
      where: {
        userName: userName,
        platform: platform,
      },
    });
  }
}
