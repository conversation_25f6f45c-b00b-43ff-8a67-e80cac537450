import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource } from 'typeorm';
import { GetResourceActionListDto } from '../../controller/resource/dtos/get-resource-action.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { ResourceActionModel } from '../../domain/model/resource-action.model';
import { IResourceActionRepository } from '../../domain/repositories/resource-action.repository';
import { ResourceActionEntity } from '../entities/resource-action.entity';
import { BaseRepository } from './base.repository';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class ResourceActionRepository
  extends BaseRepository
  implements IResourceActionRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createResourceAction(
    data: ResourceActionModel,
  ): Promise<ResourceActionModel> {
    const repository = this.getRepository(ResourceActionEntity);

    const action = repository.create({
      actionAliases: data.actionAliases,
      description: data.description,
      resourceId: data.resourceId,
      resourceIdGroup: data.resourceIdGroup,
    });

    return await repository.save(action);
  }
  async updateResourceAction(
    data: ResourceActionModel,
  ): Promise<ResourceActionModel> {
    return;
  }
  async deleteResourceAction(id: string): Promise<void> {
    const repository = this.getRepository(ResourceActionEntity);
    await repository.delete(id);
  }

  async getResourceActionById(id: string): Promise<ResourceActionModel> {
    const repository = this.getRepository(ResourceActionEntity);
    return await repository.findOneBy({ id: id });
  }

  async getResourceActions(
    conditions: GetResourceActionListDto,
  ): Promise<ResponseDto<ResourceActionModel>> {
    const repository = this.getRepository(ResourceActionEntity);
    const queryBuilder = repository.createQueryBuilder('actions');

    return await this.pagination(queryBuilder, conditions);
  }
}
