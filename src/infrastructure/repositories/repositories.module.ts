import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AzureAdStrategy } from '../config/azure-ad/azure.strategy';
import { MongooseConfigModule } from '../config/mongooes/mongooes.module';
import { RedisConfigModule } from '../config/redis/redis.module';
import { TypeOrmConfigModule } from '../config/typeorm/typeorm.module';
import { AccountEntity } from '../entities/account.entity';
import { DataPermissionEntity } from '../entities/data-permission.entity';
import { DataRoleEntity } from '../entities/data-role.entity';
import { FileImportHistoryEntity } from '../entities/file-import-history.entity';
import { ResourceActionEntity } from '../entities/resource-action.entity';
import { ResourceEntity } from '../entities/resource.entity';
import { RolePermissionEntity } from '../entities/role-permission.entity';
import { RoleEntity } from '../entities/role.entity';
import { UserVerifyEntity } from '../entities/user-verify.entity';
import { UserEntity } from '../entities/user.entity';
import { LoginLogEntity, LoginLogSchema } from '../schemas/login-log.schema';
import { RepositoryProviders } from './repositories.provider';
import { ApiLogEntity, ApiLogSchema } from '../schemas/api-log.schema';
import {
  MobileManagementEntity,
  MobileManagementSchema,
} from '../schemas/mobile-management.schema';

@Module({
  imports: [
    TypeOrmConfigModule,
    TypeOrmModule.forFeature([
      UserEntity,
      UserVerifyEntity,
      FileImportHistoryEntity,
      AccountEntity,
      DataPermissionEntity,
      RolePermissionEntity,
      RoleEntity,
      DataRoleEntity,
      ResourceEntity,
      ResourceActionEntity,
    ]),
    RedisConfigModule,
    MongooseConfigModule,
    MongooseModule.forFeature([
      { name: LoginLogEntity.name, schema: LoginLogSchema },
      { name: ApiLogEntity.name, schema: ApiLogSchema },
      { name: MobileManagementEntity.name, schema: MobileManagementSchema },
    ]),
  ],
  providers: [...RepositoryProviders, AzureAdStrategy],
  exports: [...RepositoryProviders],
})
export class RepositoriesModule {}
