import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateApiLogDto } from '../../controller/api-log/dtos/create-api-log.dto';
import { GetListApiLogDto } from '../../controller/api-log/dtos/get-list-api-log.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { ApiLogModel } from '../../domain/model/api-log.model';
import { IApiLogRepository } from '../../domain/repositories/api-log.repository';
import { BaseMongoRepository } from './base-mongo.repository';
import { ApiLogEntity } from '../schemas/api-log.schema';

@Injectable()
export class ApiLogRepository
  extends BaseMongoRepository<ApiLogEntity>
  implements IApiLogRepository
{
  constructor(
    @InjectModel(ApiLogEntity.name)
    protected model: Model<ApiLogEntity>,
  ) {
    super(model);
  }

  async create(data: CreateApiLogDto): Promise<ApiLogModel> {
    const doc = await this.model.create(data);
    const model = doc.toObject();
    return model;
  }

  async getList(
    conditions: GetListApiLogDto,
  ): Promise<ResponseDto<ApiLogModel>> {
    const query = this.buildFindQuery(conditions);
    return await this.pagination(conditions, query);
  }

  private buildFindQuery(conditions: GetListApiLogDto): any {
    const query: any = {};

    if (conditions.controller)
      query['controller'] = { $regex: conditions.controller, $options: 'i' };
    if (conditions.route)
      query['route'] = { $regex: conditions.route, $options: 'i' }; // Tìm kiếm gần đúng
    if (conditions.method) query['method'] = conditions.method;
    if (conditions.statusCode) query['statusCode'] = conditions.statusCode;
    if (conditions.isSuccess !== undefined)
      query['isSuccess'] = conditions.isSuccess;

    return query;
  }
}
