import { Inject, Injectable, Scope } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { IFileImportHistoryRepository } from '../../domain/repositories/file-import-history.repository';
import { DataSource, Repository } from 'typeorm';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { REQUEST } from '@nestjs/core';
import { FileImportHistoryModel } from '../../domain/model/file-import-history.model';
import { FileImportHistoryEntity } from '../entities/file-import-history.entity';
import { GetListFileImportHistoryDto } from '../../controller/file-import-history/dtos/get-list-file-import-history.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { IntersectionType } from '@nestjs/swagger';

@Injectable({ scope: Scope.REQUEST })
export class FileImportHistoryRepository
  extends BaseRepository
  implements IFileImportHistoryRepository
{
  constructor(
    @InjectRepository(FileImportHistoryEntity)
    private readonly fileImportHistoryEntityRepository: Repository<FileImportHistoryEntity>,
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createFileImportHistory(
    data: FileImportHistoryModel,
  ): Promise<FileImportHistoryModel> {
    // const repository = this.getRepository(FileImportHistoryEntity); tắt transaction
    const newFileImportHistory = this.fileImportHistoryEntityRepository.create({
      ...data,
    });
    return await this.fileImportHistoryEntityRepository.save(
      newFileImportHistory,
    );
  }

  async getListFileImportHistory(
    conditions: GetListFileImportHistoryDto,
  ): Promise<ResponseDto<FileImportHistoryModel>> {
    const repository = this.getRepository(FileImportHistoryEntity);
    const queryBuilder = repository.createQueryBuilder('fileImportHistories');

    queryBuilder.where('fileImportHistories.importType IN (:...importTypes)', {
      importTypes: conditions.importTypes,
    });

    queryBuilder.orderBy('fileImportHistories.createdAt', 'DESC');
    return await this.pagination(queryBuilder, conditions);
  }

  async deleteFileImportHistory(id: string): Promise<void> {
    const repository = this.getRepository(FileImportHistoryEntity);
    await repository.softDelete(id);
  }

  async updateFileImportHistory(
    data: FileImportHistoryModel,
  ): Promise<FileImportHistoryModel> {
    // const repository = this.getRepository(FileImportHistoryEntity); tắt transaction
    const updateFileImportHistory =
      this.fileImportHistoryEntityRepository.create({ ...data });
    return await this.fileImportHistoryEntityRepository.save(
      updateFileImportHistory,
    );
  }

  async getFileImportHistoryById(id: string): Promise<FileImportHistoryModel> {
    const repository = this.getRepository(FileImportHistoryEntity);
    return await repository.findOne({ where: { id } });
  }
}
