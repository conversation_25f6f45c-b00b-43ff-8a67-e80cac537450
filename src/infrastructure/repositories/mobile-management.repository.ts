import { Injectable } from '@nestjs/common';
import { BaseMongoRepository } from './base-mongo.repository';
import { IMobileManagementRepository } from '../../domain/repositories/mobile-management.repository';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { MobileManagementEntity } from '../schemas/mobile-management.schema';
import { MobileManagementModel } from '../../domain/model/mobile-management.model';
import { CreateMobileManagementDto } from '../../controller/mobile-management/dtos/create-mobile-management.dto';
import { UpdateMobileManagementDto } from '../../controller/mobile-management/dtos/update-mobile-management.dto';
import { EPlatform } from '../../domain/config/enums/platform.enum';

@Injectable()
export class MobileManagementRepository
  extends BaseMongoRepository<MobileManagementEntity>
  implements IMobileManagementRepository
{
  constructor(
    @InjectModel(MobileManagementEntity.name)
    protected model: Model<MobileManagementEntity>,
  ) {
    super(model);
  }

  async getDetailMobileManagement(
    platform: EPlatform,
  ): Promise<MobileManagementEntity> {
    return await this.model
      .findOne({
        platform,
      })
      .exec();
  }

  async createMobileManagement(
    data: CreateMobileManagementDto,
  ): Promise<MobileManagementEntity> {
    const doc = await this.model.create(data);
    const model = doc.toObject();
    return model;
  }

  async updateMobileManagement(
    id: string,
    data: UpdateMobileManagementDto,
  ): Promise<MobileManagementEntity> {
    const doc = await this.model.findOneAndUpdate({ _id: id }, data);
    const model = doc.toObject();
    return model;
  }
}
