import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Scope,
} from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, In } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { GetRoleListDto } from '../../controller/role/dtos/get-role-list.dto';
import { UpdateRoleDto } from '../../controller/role/dtos/update-role.dto';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { RoleModel } from '../../domain/model/role.model';
import { IRoleRepository } from '../../domain/repositories/role.repository';
import { removeUnicode, uidToPath } from '../../utils/common';
import { RoleEntity } from '../entities/role.entity';
import { BaseRepository } from './base.repository';
import { EPlatform } from '../../domain/config/enums/platform.enum';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class RoleRepository extends BaseRepository implements IRoleRepository {
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }

  async createFirstRole(data: RoleModel): Promise<RoleModel> {
    const repository = this.getRepository(RoleEntity);
    const role = repository.create({
      farmId: data.farmId,
      name: data.name,
      description: data.description,
    });
    await repository.save(role);
    await repository.update(role.id, { path: `${uidToPath(role.id)}` });

    return role;
  }
  async createRole(data: RoleModel): Promise<RoleModel> {
    const repository = this.getRepository(RoleEntity);

    const role = repository.create({
      ...data,
    });

    await repository.save(role);

    const partialEntity: QueryDeepPartialEntity<RoleEntity> = {
      path: `${uidToPath(role.id)}`,
    };

    if (data.parentId) {
      const parentRole = await repository.findOne({
        where: { id: data.parentId },
      });

      if (parentRole) {
        partialEntity.parentId = parentRole.id;
        partialEntity.path = `${parentRole.path || ''}.${uidToPath(role.id)}`;
      }
    }

    await repository.update({ id: role.id }, partialEntity);

    return role;
  }
  async updateRole(id: string, data: UpdateRoleDto): Promise<RoleModel> {
    const repository = this.getRepository(RoleEntity);

    const role = repository.create({
      id,
      ...data,
    });

    return await repository.save(role);
  }
  async deleteRole(id: string): Promise<void> {
    const repository = this.getRepository(RoleEntity);
    await repository.softDelete(id);
  }
  async getRoleById(id: string): Promise<RoleModel> {
    const repository = this.getRepository(RoleEntity);
    return await repository.findOneBy({ id: id });
  }
  async getRoles(conditions: GetRoleListDto): Promise<ResponseDto<RoleModel>> {
    const repository = this.getRepository(RoleEntity);
    const queryBuilder = repository
      .createQueryBuilder('roles')
      .orderBy('roles.createdAt', 'DESC');

    if (conditions.statuses && conditions.statuses.length) {
      queryBuilder.andWhere({ status: In(conditions.statuses) });
    }

    if (conditions.platforms && conditions.platforms.length) {
      queryBuilder.innerJoin('roles.rolePermissions', 'rp');
      queryBuilder.innerJoin('rp.resource', 'r');
      queryBuilder.andWhere('r.platform IN (:...platforms)', {
        platforms: conditions.platforms,
      });
    }

    if (conditions.searchString) {
      queryBuilder.andWhere(
        '(roles.search_value ILIKE :searchString OR roles.code ILIKE :searchCode)',
        {
          searchString: `%${removeUnicode(conditions.searchString)}%`,
          searchCode: conditions.searchString,
        },
      );
    }

    if (conditions.isAssigned) {
      queryBuilder.innerJoin('roles.users', 'users').withDeleted();
    }

    return await this.pagination(queryBuilder, {
      ...conditions,
      searchString: null,
    });
  }

  async getRolesByUserId(userId: string) {
    const repository = this.getRepository(RoleEntity);

    return await repository.find({
      where: {
        users: {
          id: userId,
        },
      },
      relations: ['users'],
    });
  }

  async countUsersByRoleId(roleId: string): Promise<number> {
    const repository = this.getRepository(RoleEntity);

    return await repository
      .createQueryBuilder('roles')
      .innerJoin('roles.users', 'users')
      .where('roles.id = :roleId', { roleId: roleId })
      .getCount();
  }

  async getRoleByCodes(
    codes: string[],
    platform: string,
  ): Promise<RoleModel[]> {
    const repository = this.getRepository(RoleEntity);
    const queryBuilder = repository.createQueryBuilder('roles');

    queryBuilder.andWhere('roles.code IN (:...codes)', {
      codes,
    });

    queryBuilder.andWhere('roles.platform = :platform', { platform });

    return await queryBuilder.getMany();
  }

  async getRolesForDDL(farmId: string): Promise<RoleModel[]> {
    try {
      const repository = this.getRepository(RoleEntity);

      return await repository
        .createQueryBuilder('roles')
        .select(['roles.id', 'roles.name'])
        .where('roles.farmId = :farmId or roles.farmId is null', { farmId })
        .andWhere('roles.platform = :platform', {
          platform: EPlatform.DIGI_AQUA,
        })
        .getMany();
    } catch (error) {
      throw new HttpException(
        error.driverError.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAll(platform: string): Promise<RoleModel[]> {
    try {
      const repository = this.getRepository(RoleEntity);

      return await repository
        .createQueryBuilder('roles')
        .select(['roles.id', 'roles.name'])
        .where('roles.platform = :platform', {
          platform: platform,
        })
        .getMany();
    } catch (error) {
      throw new HttpException(
        error.driverError.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getByUser(userId: string, platform: EPlatform): Promise<RoleModel[]> {
    try {
      const repository = this.getRepository(RoleEntity);
      return await repository.find({
        select: ['id', 'name'],
        where: { users: { id: userId }, platform: platform },
      });
    } catch (error) {
      throw new HttpException(
        error.driverError.message,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getRoleForApproverByPosition(
    conditions: GetRoleListDto,
  ): Promise<RoleModel[]> {
    const repository = this.getRepository(RoleEntity);
    const queryBuilder = repository
      .createQueryBuilder('roles')
      .orderBy('roles.createdAt', 'DESC');

    if (conditions.statuses && conditions.statuses.length) {
      queryBuilder.andWhere({ status: In(conditions.statuses) });
    }

    if (conditions.platforms && conditions.platforms.length) {
      queryBuilder.innerJoin('roles.rolePermissions', 'rp');
      queryBuilder.innerJoin('rp.resource', 'r');
      queryBuilder.andWhere('r.platform IN (:...platforms)', {
        platforms: conditions.platforms,
      });
    }

    return await queryBuilder.getMany();
  }

  async getRoleAllData(): Promise<RoleModel> {
    const repository = this.getRepository(RoleEntity);

    const queryBuilder = repository.createQueryBuilder('roles');

    queryBuilder.andWhere('(roles.name = :name OR roles.name = :nameSe)', {
      name: 'All Data',
      nameSe: 'All Data ',
    });

    return await queryBuilder.getOne();
  }
}
