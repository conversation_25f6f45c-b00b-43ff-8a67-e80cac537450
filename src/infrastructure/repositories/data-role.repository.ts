import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { InjectDataSource } from '@nestjs/typeorm';
import * as dayjs from 'dayjs';
import * as timezone from 'dayjs/plugin/timezone';
import * as utc from 'dayjs/plugin/utc';
import { DataSource, In } from 'typeorm';
import { GetDataRoleListDto } from '../../controller/data-role/dtos/get-data-role-list.dto';
import { UpdateDataRoleDto } from '../../controller/data-role/dtos/update-data-role.dto';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { ResponseDto } from '../../domain/dtos/response.dto';
import { DataRoleModel } from '../../domain/model/data-role.model';
import { IDataRoleRepository } from '../../domain/repositories/data-role.repository';
import { DataRoleEntity } from '../entities/data-role.entity';
import { BaseRepository } from './base.repository';
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable({ scope: Scope.REQUEST })
export class DataRoleRepository
  extends BaseRepository
  implements IDataRoleRepository
{
  constructor(
    @InjectDataSource()
    dataSource: DataSource,
    @Inject(REQUEST) req: Request,
  ) {
    super(dataSource, req);
  }
  async createDataRole(data: DataRoleModel): Promise<DataRoleModel> {
    const repository = this.getRepository(DataRoleEntity);

    const newData = repository.create({
      description: data.description,
      refCode: data.refCode,
      refId: data.refId,
      parentId: data.parentId,
      type: data.type,
      platform: data.platform,
    });

    return await repository.save(newData);
  }
  async updateDataRole(
    dataRoleId: string,
    updateDataRoleDto: UpdateDataRoleDto,
  ): Promise<DataRoleModel> {
    const repository = this.getRepository(DataRoleEntity);
    const updateDataRole = repository.create({
      id: dataRoleId,
      ...updateDataRoleDto,
    });
    return await repository.save(updateDataRole);
  }
  async deleteDataRole(refId: string): Promise<void> {
    const repository = this.getRepository(DataRoleEntity);
    await repository.softDelete({ refId });
  }
  async getDataRoleById(id: string): Promise<DataRoleModel> {
    const repository = this.getRepository(DataRoleEntity);
    return await repository.findOneBy({ id: id });
  }
  async getDataRoles(
    conditions: GetDataRoleListDto,
  ): Promise<ResponseDto<DataRoleModel>> {
    const repository = this.getRepository(DataRoleEntity);

    const queryBuilder = repository.createQueryBuilder('dataRoles');

    queryBuilder.andWhere('dataRoles.isEnabled = :isEnabled', {
      isEnabled: true,
    });

    if (conditions.ids && conditions.ids.length) {
      queryBuilder.andWhere({ id: In(conditions.ids) });
    }

    if (conditions.types && conditions.types.length) {
      queryBuilder.andWhere({ type: In(conditions.types) });
    }

    if (conditions.platform) {
      queryBuilder.andWhere({ platform: conditions.platform });
    } else {
      return new ResponseDto([], conditions.page, conditions.limit, 0);
    }

    return await this.pagination(queryBuilder, conditions);
  }

  async getDataRoleByRoleId(roleId: string, platform: EPlatform) {
    const repository = this.getRepository(DataRoleEntity);
    const result = await repository.query(
      `
          WITH cte_role as (SELECT id AS "roleId", allow_inherit AS "allowInherit", nlevel(path) AS "nlevel", code
                            FROM roles rlb
                            WHERE path @> (SELECT rl."path"
                                            FROM roles rl
                                            WHERE rl."path" ~ $1) AND path != '' AND rlb.deleted_at IS NULL
          ORDER BY nlevel(path) DESC
              )
          SELECT cte_role.*,
                 dp.is_personal AS "isPersonal",
                 dp.allow_view AS "allowView",
                 dr.id AS "dataRoleId",
                 dr.ref_id AS "refId",
                 dr.ref_code AS "refCode",
                 dr.type AS "type",
                 dr.parent_id AS "parentId",
                 dr.description AS "description",
                 dr.is_enabled AS "isEnabled",
                 dr.platform AS "platform"
          FROM cte_role
          LEFT JOIN data_permissions dp ON cte_role."roleId" = dp.role_id AND dp.deleted_at IS NULL
          LEFT JOIN data_roles dr ON dr.id = dp.data_role_id AND dr.deleted_at IS NULL
          WHERE dr.platform = $2
          ORDER BY cte_role.nlevel DESC`,
      ['*.' + roleId.replace(/-/g, ''), platform],
    );

    return result;
  }

  async getDataRoleByRefId(refId: string): Promise<DataRoleModel> {
    const repository = this.getRepository(DataRoleEntity);
    return await repository.findOneBy({ refId: refId });
  }
}
