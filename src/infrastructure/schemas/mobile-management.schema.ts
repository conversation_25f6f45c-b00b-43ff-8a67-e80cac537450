import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { EPlatform } from '../../domain/config/enums/platform.enum';

@Schema({
  collection: 'mobile_managements',
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class MobileManagementEntity extends Document {
  @Prop({ required: true }) androidVersion: string;
  @Prop({ required: true }) iosVersion: string;
  @Prop({ required: true }) isShowAccountManagement: boolean;
  @Prop({
    type: String,
    enum: Object.values(EPlatform),
    required: true,
    index: true,
  })
  platform: string;
  @Prop({ required: true }) isForceUpdateAndroid: boolean;
  @Prop({ required: true }) isForceUpdateIos: boolean;
}

export const MobileManagementSchema = SchemaFactory.createForClass(
  MobileManagementEntity,
);
