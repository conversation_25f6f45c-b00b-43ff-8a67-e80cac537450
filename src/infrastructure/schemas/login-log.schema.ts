import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { HydratedDocument } from 'mongoose';

export type NotificationEntityDocument = HydratedDocument<LoginLogEntity>;
@Schema({
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true },
})
export class LoginLogEntity {
  _id?: mongoose.Types.ObjectId;
  @Prop({
    type: Date,
    required: true,
    default: Date.now,
    index: true,
    expires: '90d',
  })
  timestamp: Date;

  @Prop({ type: String, required: false })
  userId: string;
  @Prop({ type: String, required: false })
  userName: string;
  @Prop({ type: String, required: false })
  firstName: string;
  @Prop({ type: String, required: false })
  lastName: string;
  @Prop({ type: String, required: false })
  email: string;
  @Prop({ type: String, required: false })
  ip: string;
  @Prop({ type: String, required: false })
  message: string;
  @Prop({ type: String, required: false })
  platform: string;
  @Prop({ type: String, required: false })
  portal: string;
}

export const LoginLogSchema = SchemaFactory.createForClass(LoginLogEntity);
