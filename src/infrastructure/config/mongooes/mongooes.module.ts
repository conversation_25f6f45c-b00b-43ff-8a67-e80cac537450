import { Module } from '@nestjs/common';
import { EnvironmentConfigModule } from '../environment-config/environment-config.module';
import { MongooseModule } from '@nestjs/mongoose';
import { EnvironmentConfigService } from '../environment-config/environment-config.service';

const config: EnvironmentConfigService = new EnvironmentConfigService();

@Module({
  imports: [
    MongooseModule.forRootAsync({
      imports: [EnvironmentConfigModule],
      inject: [EnvironmentConfigService],
      useFactory: async () => ({
        uri: config.getMongoDbUri(),
      }),
    }),
  ],
})
export class MongooseConfigModule {}
