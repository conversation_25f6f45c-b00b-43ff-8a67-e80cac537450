import { plainToClass } from 'class-transformer';
import { validateSync } from 'class-validator';

enum Environment {
  Development = 'development',
  Production = 'production',
  Local = 'local',
  Test = 'test',
}

class EnvironmentVariables {
  NODE_ENV: Environment;

  JWT_SECRET: string;
  JWT_EXPIRATION_TIME: string;
  JWT_REFRESH_TOKEN_SECRET: string;
  JWT_REFRESH_TOKEN_EXPIRATION_TIME: string;

  DATABASE_HOST: string;
  DATABASE_PORT: number;
  DATABASE_USER: string;
  DATABASE_PASSWORD: string;
  DATABASE_NAME: string;
  DATABASE_SCHEMA: string;
  DATABASE_SYNCHRONIZE: boolean;

  REDIS_HOST: string;
  REDIS_PORT: number;
  REDIS_USERNAME: string;
  REDIS_PASSWORD: string;
  getRedisTLS: boolean;

  MAIL_SMTP_HOST: string;
  MAIL_SMTP_PORT: number;
  MAIL_SMTP_USER: string;
  MAIL_SMTP_SECURE: boolean;

  OTP_MAX_TIME_SENT: number;
  OTP_MAX_TIME_STAMP_SENT: number;
  OTP_EXPIRE_TIME: number;
  OTP_TOKEN_EXPIRE_TIME: number;

  AZURE_AD_CLIENT_ID: string;
  AZURE_AD_CLIENT_SECRET: string;
  AZURE_AD_TENANT_ID: string;
  AZURE_AD_REFRESH_TOKEN: string;
  AZURE_AD_ACCESS_URL: string;

  MONGO_DB_URI: string;
}

export function validate(config: Record<string, unknown>) {
  const validatedConfig = plainToClass(EnvironmentVariables, config, {
    enableImplicitConversion: true,
  });
  const errors = validateSync(validatedConfig, {
    skipMissingProperties: false,
  });

  if (errors.length > 0) {
    //throw new Error(errors.toString());
  }
  return validatedConfig;
}
