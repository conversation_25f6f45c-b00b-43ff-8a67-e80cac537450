import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DatabaseConfig } from '../../../domain/config/database.interface';
import { EmailConfig } from '../../../domain/config/email.interface';
import { OtpConfig } from '../../../domain/config/otp.interface';
import { RedisConfig } from '../../../domain/config/redis.interface';

@Injectable()
export class EnvironmentConfigService
  implements DatabaseConfig, RedisConfig, EmailConfig, OtpConfig
{
  private configService: ConfigService = new ConfigService();
  // constructor(private configService: ConfigService) {}

  // getRabbitMQHost(): string {
  //   return this.configService.get<string>('RABBITMQ_HOST');
  // }
  // getRabbitMQUser(): string {
  //   return this.configService.get<string>('RABBITMQ_USER');
  // }
  // getRabbitMQPassword(): string {
  //   return this.configService.get<string>('RABBITMQ_PASSWORD');
  // }

  // getRabbitMQQueueService(): string {
  //   return this.configService.get<string>('RABBITMQ_QUEUE_SERVICE');
  // }

  // getRabbitMQUri(): string {
  //   return `amqp://${this.getRabbitMQUser()}:${this.getRabbitMQPassword()}@${this.getRabbitMQHost()}`;
  // }

  getDatabaseHost(): string {
    return this.configService.get<string>('DATABASE_HOST');
  }

  getDatabasePort(): number {
    return this.configService.get<number>('DATABASE_PORT');
  }

  getDatabaseUser(): string {
    return this.configService.get<string>('DATABASE_USER');
  }

  getDatabasePassword(): string {
    return this.configService.get<string>('DATABASE_PASSWORD');
  }

  getDatabaseName(): string {
    return this.configService.get<string>('DATABASE_NAME');
  }

  getDatabaseSchema(): string {
    return this.configService.get<string>('DATABASE_SCHEMA');
  }

  getDatabaseSync(): boolean {
    return this.configService.get<string>('DATABASE_SYNCHRONIZE') === 'true';
  }

  getDatabaseHostSlave1(): string {
    return this.configService.get<string>('DATABASE_HOST_SLAVE_1');
  }

  ///REDIS
  getRedisHost(): string {
    return this.configService.get<string>('REDIS_HOST');
  }

  getRedisPort(): number {
    return this.configService.get<number>('REDIS_PORT');
  }

  getRedisUserName(): string {
    return this.configService.get<string>('REDIS_USERNAME');
  }

  getRedisPassword(): string {
    return this.configService.get<string>('REDIS_PASSWORD');
  }

  getRedisTLS(): boolean {
    return this.configService.get<string>('REDIS_TLS') === 'true';
  }

  ///NODE MAILER
  getMailHost(): string {
    return this.configService.get<string>('MAIL_SMTP_HOST');
  }

  getMailPort(): number {
    return this.configService.get<number>('MAIL_SMTP_PORT');
  }

  getMailUser(): string {
    return this.configService.get<string>('MAIL_SMTP_USER');
  }

  getMailPass(): string {
    return this.configService.get<string>('MAIL_SMTP_PASS');
  }

  getMailSecure(): boolean {
    return this.configService.get<string>('MAIL_SMTP_SECURE') === 'true';
  }
  getMailCC(): string {
    return this.configService.get<string>('MAIL_CC');
  }

  getOtpMaxTimeSent(): number {
    return Number(this.configService.get<string>('OTP_MAX_TIME_SENT') || 5);
  }
  getOtpMaxTimeStampSent(): number {
    return Number(
      this.configService.get<string>('OTP_MAX_TIME_STAMP_SENT') || 2,
    );
  }
  getOtpExpireTime(): number {
    return Number(this.configService.get<string>('OTP_EXPIRE_TIME') || 180000);
  }
  getOtpTokenExpireTime(): number {
    return Number(
      this.configService.get<string>('OTP_TOKEN_EXPIRE_TIME') || 180,
    );
  }

  //AZURE AD
  getAzureADClientId(): string {
    return this.configService.get<string>('AZURE_AD_CLIENT_ID');
  }
  getAzureADClientSecret(): string {
    return this.configService.get<string>('AZURE_AD_CLIENT_SECRET');
  }
  getAzureADTenantId(): string {
    return this.configService.get<string>('AZURE_AD_TENANT_ID');
  }

  getAzureADRefreshToken(): string {
    return this.configService.get<string>('AZURE_AD_REFRESH_TOKEN');
  }

  getAzureADAccessUrl(): string {
    return this.configService.get<string>('AZURE_AD_ACCESS_URL');
  }
  /* MONGO */
  getMongoDbUri(): string {
    console.log(24, this.configService.get<string>('MONGO_DB_URI'));
    return this.configService.get<string>('MONGO_DB_URI');
  }
}
