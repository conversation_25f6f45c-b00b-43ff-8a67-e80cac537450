import { Injectable, OnModuleInit } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { ChannelsService } from './channels.service';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

@WebSocketGateway(parseInt(process.env.WEBSOCKET_PORT), {
  cors: {
    origin: '*',
  },
})
@Injectable()
export class SocketGateway implements OnModuleInit {
  @WebSocketServer()
  server: Server;

  constructor(private readonly channelService: ChannelsService) {}
  onModuleInit() {
    // ...
  }

  @SubscribeMessage('joinVersionManagement')
  async versionManagement(
    client: Socket,
    data: {
      platform: EPlatform;
    },
  ): Promise<void> {
    console.log('join private Room:', data.platform);
    client.join(this.channelService.versionManagementChannel(data.platform));
  }

  @SubscribeMessage('leaveVersionManagement')
  leaveVersionManagement(
    @MessageBody() data: { platform: EPlatform },
    @ConnectedSocket() client: Socket,
  ) {
    client.leave(this.channelService.versionManagementChannel(data.platform));
  }
}
