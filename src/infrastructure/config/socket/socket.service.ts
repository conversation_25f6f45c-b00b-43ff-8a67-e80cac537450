import { Injectable, Logger } from '@nestjs/common';
import { ChannelsService } from './channels.service';
import { EPlatform } from '../../../domain/config/enums/platform.enum';
import { MobileManagementEntity } from '../../schemas/mobile-management.schema';
import { SocketGateway } from './socket.gateway';

@Injectable()
export class SocketService {
  private readonly logger = new Logger(SocketService.name);

  constructor(
    private readonly gateway: SocketGateway,
    private readonly channelService: ChannelsService,
  ) {}

  versionManagement(platform: EPlatform, data: MobileManagementEntity) {
    const roomIds: string | string[] =
      this.channelService.versionManagementChannel(platform);

    this.gateway.server
      .to(roomIds)
      .emit(`${process.env.ENV}-version-receiveMessage`, data);
  }
}
