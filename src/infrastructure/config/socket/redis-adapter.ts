import { IoAdapter } from '@nestjs/platform-socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient } from 'redis';
import { ServerOptions } from 'socket.io';
import { EnvironmentConfigService } from '../environment-config/environment-config.service';

export class RedisIoAdapter extends IoAdapter {
  private adapterConstructor: ReturnType<typeof createAdapter>;
  private readonly configService: EnvironmentConfigService;

  async connectToRedis(): Promise<void> {
    const pubClient = createClient({
      socket: {
        host: this.configService.getRedisHost() || 'localhost',
        port: Number(this.configService.getRedisPort()) || 6379,
      },
      username: 'default', // Nếu Redis không yêu cầu username, có thể bỏ qua
      password: this.configService.getRedisPassword() || '',
    });
    const subClient = pubClient.duplicate();

    await Promise.all([pubClient.connect(), subClient.connect()]);

    this.adapterConstructor = createAdapter(pubClient, subClient);
  }

  createIOServer(port: number, options?: ServerOptions): any {
    const server = super.createIOServer(port, options);
    server.adapter(this.adapterConstructor);
    return server;
  }
}
