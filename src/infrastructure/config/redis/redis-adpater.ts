import { IoAdapter } from '@nestjs/platform-socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient } from 'redis';
import { ServerOptions } from 'socket.io';
import { EnvironmentConfigService } from '../environment-config/environment-config.service';
import { RedisOptions } from '@nestjs/microservices';

const config: EnvironmentConfigService = new EnvironmentConfigService();

export class RedisIoAdapter extends IoAdapter {
  private adapterConstructor: ReturnType<typeof createAdapter>;

  async connectToRedis(): Promise<void> {
    console.log(14, {
      host: config.getRedisHost() || 'localhost',
      port: Number(config.getRedisPort()) || 6379,
      password: config.getRedisPassword() || '',
    });
    const pubClient = createClient({
      socket: {
        host: config.getRedisHost() || 'localhost',
        port: Number(config.getRedisPort()) || 6379,
      },
      username: 'default', // Nếu Redis không yêu cầu username, có thể bỏ qua
      password: config.getRedisPassword() || '',
    });
    const subClient = pubClient.duplicate();

    await Promise.all([pubClient.connect(), subClient.connect()]);

    this.adapterConstructor = createAdapter(pubClient, subClient);
  }

  createIOServer(port: number, options?: ServerOptions): any {
    const server = super.createIOServer(port, options);
    server.adapter(this.adapterConstructor);
    return server;
  }
}
