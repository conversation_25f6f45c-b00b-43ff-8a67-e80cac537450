import { RedisModule } from '@nestjs-modules/ioredis';
import { Module } from '@nestjs/common';
import { RedisUsecases } from '../../../usecases/redis.usecases';
import { EnvironmentConfigModule } from '../environment-config/environment-config.module';
import { EnvironmentConfigService } from '../environment-config/environment-config.service';

const config: EnvironmentConfigService = new EnvironmentConfigService();

@Module({
  imports: [
    // CacheModule.registerAsync({
    //   imports: [EnvironmentConfigModule],
    //   inject: [EnvironmentConfigService],
    //   useFactory: async (config: EnvironmentConfigService) => ({
    //     host: config.getRedisHost(),
    //     port: config.getRedisPort(),
    //   }),
    //   isGlobal: true,
    // }),
    RedisModule.forRootAsync({
      imports: [EnvironmentConfigModule],
      inject: [EnvironmentConfigService],
      useFactory: () => ({
        type: 'single',
        options: {
          host: config.getRedisHost(),
          port: config.getRedisPort(),
          username: config.getRedisUserName(),
          password: config.getRedisPassword(),
          tls: config.getRedisTLS() ? { rejectUnauthorized: false } : undefined,
        },
      }),
    }),
  ],
  providers: [RedisUsecases],
  exports: [RedisUsecases],
})
export class RedisConfigModule {}
