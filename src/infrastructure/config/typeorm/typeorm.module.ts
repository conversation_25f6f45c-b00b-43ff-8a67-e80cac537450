import { Module } from '@nestjs/common';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { DataSource, DataSourceOptions } from 'typeorm';
import { EnvironmentConfigModule } from '../environment-config/environment-config.module';
import { EnvironmentConfigService } from '../environment-config/environment-config.service';
import CustomNamingStrategy from './typeorm-custom-name-strategy';

const config: EnvironmentConfigService = new EnvironmentConfigService();

export const getTypeOrmModuleOptions = {
  type: 'postgres',
  replication: {
    master: {
      host: config.getDatabaseHost(),
      port: config.getDatabasePort(),
      username: config.getDatabaseUser(),
      password: config.getDatabasePassword(),
      database: config.getDatabaseName(),
    },
    slaves: [
      {
        host: config.getDatabaseHostSlave1(),
        port: config.getDatabasePort(),
        username: config.getDatabaseUser(),
        password: config.getDatabasePassword(),
        database: config.getDatabaseName(),
      },
    ],
  },
  entities: [__dirname + './../../**/*.entity{.ts,.js}'],
  synchronize: false,
  // schema: process.env.DATABASE_SCHEMA,
  autoLoadEntities: true,
  // migrationsRun: process.env.ENV != 'PRODUCTION',
  migrationsRun: true,
  migrations: [__dirname + '/../../../migrations/**/*{.ts,.js}'],
  logging: process.env.ENV != 'PRODUCTION',
  cli: {
    migrationsDir: __dirname + '../../../migrations',
  },
  // ssl: {
  //    rejectUnauthorized: true,
  // },
  namingStrategy: new CustomNamingStrategy(),
};

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [EnvironmentConfigModule],
      inject: [EnvironmentConfigService],
      useFactory: (): TypeOrmModuleOptions => {
        return getTypeOrmModuleOptions as TypeOrmModuleOptions;
      },
    }),
  ],
})
export class TypeOrmConfigModule {}

export default new DataSource(getTypeOrmModuleOptions as DataSourceOptions);
