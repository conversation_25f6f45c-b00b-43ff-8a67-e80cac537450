import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { DataRoleType } from '../../domain/config/enums/data-role-type.enum';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { DataPermissionEntity } from './data-permission.entity';

@Entity('data_roles')
export class DataRoleEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  description: string;

  @Column({ type: 'varchar' })
  refId: string;

  @Column({ type: 'varchar' })
  refCode: string;

  @Column({ type: 'uuid', nullable: true })
  parentId: string;

  @Column({
    type: 'enum',
    enum: Object.values(DataRoleType),
    default: DataRoleType.COMPANY,
  })
  type: DataRoleType;

  @Column({ type: 'boolean', default: true })
  isEnabled: boolean;

  @Column({
    enum: Object.values(EPlatform),
    default: EPlatform.E_PURCHASE,
    nullable: true,
  })
  platform: EPlatform;

  @ManyToOne(() => DataRoleEntity, (dataRole) => dataRole.children) // Inverted relationship for clarity
  parent: DataRoleEntity | null;

  @OneToMany(() => DataRoleEntity, (dataRole) => dataRole.parent) // Define children explicitly
  children: DataRoleEntity[];

  @OneToMany(
    () => DataPermissionEntity,
    (dataPermission) => dataPermission.dataRole,
  )
  dataPermissions: DataPermissionEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue =
      removeUnicode(this.description) + ' ' + removeUnicode(this.refCode);
  }
}
