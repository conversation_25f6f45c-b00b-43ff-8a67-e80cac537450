import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntity } from './base.entity';
import { ResourceActionEntity } from './resource-action.entity';
import { ResourceEntity } from './resource.entity';
import { RoleEntity } from './role.entity';

@Entity('role_permissions')
export class RolePermissionEntity extends BaseEntity {
  @Column({ type: 'uuid' })
  roleId: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  actionView: boolean;
  @Column({ type: 'boolean', default: false, nullable: true })
  actionCreate: boolean;
  @Column({ type: 'boolean', default: false, nullable: true })
  actionEdit: boolean;
  @Column({ type: 'boolean', default: false, nullable: true })
  actionDelete: boolean;

  @Column({ type: 'uuid' })
  resourceId: string;

  @Column({ type: 'uuid', nullable: true })
  resourceActionId: string;

  @ManyToOne(() => RoleEntity, (role) => role.rolePermissions) // Inverted relationship for clarity
  role: RoleEntity | null;

  @ManyToOne(() => ResourceEntity, (resource) => resource.rolePermissions) // Inverted relationship for clarity
  resource: ResourceEntity | null;

  @ManyToOne(
    () => ResourceActionEntity,
    (resourceAction) => resourceAction.rolePermissions,
  ) // Inverted relationship for clarity
  resourceAction: ResourceActionEntity | null;
}
