import {
  BeforeInsert,
  Column,
  Entity,
  Index,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { EStatus } from '../../domain/config/enums/status.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { DataPermissionEntity } from './data-permission.entity';
import { RolePermissionEntity } from './role-permission.entity';
import { UserEntity } from './user.entity';

@Entity('roles')
@Index(['code', 'platform'], { where: 'deleted_at IS NULL' })
export class RoleEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  description: string;

  @Column({ type: 'uuid', nullable: true })
  parentId: string;

  @Column({ type: 'boolean', default: true })
  allowInherit: boolean;

  @Column({ type: 'ltree', nullable: true })
  path: string;

  @Column({ type: 'varchar', default: EStatus.ACTIVE, nullable: true })
  status: EStatus;

  @Column({ type: 'varchar', nullable: true })
  code: string;

  @Column({
    enum: Object.values(EPlatform),
    default: EPlatform.E_PURCHASE,
    nullable: true,
  })
  platform: EPlatform;

  @ManyToOne(() => RoleEntity, (role) => role.children) // Inverted relationship for clarity
  parent: RoleEntity | null;

  @OneToMany(() => RoleEntity, (role) => role.parent) // Define children explicitly
  children: RoleEntity[];

  @OneToMany(
    () => RolePermissionEntity,
    (rolePermission) => rolePermission.role,
  ) // Define children explicitly
  rolePermissions: RolePermissionEntity[];

  @ManyToMany(() => UserEntity, (user) => user.roles)
  users: UserEntity[];

  @OneToMany(
    () => DataPermissionEntity,
    (dataPermission) => dataPermission.role,
  ) // Define children explicitly
  dataPermissions: DataPermissionEntity[];

  @Column({ name: 'farm_id', type: 'uuid', nullable: true })
  farmId: string;

  @BeforeInsert()
  convertSearchValue() {
    this.searchValue = removeUnicode(this.name);
  }

  @BeforeInsert()
  async generateCode() {
    const lastUser = await RoleEntity.find({
      where: {
        platform: this.platform || EPlatform.E_PURCHASE,
      },
      select: { code: true },
      order: { createdAt: 'DESC' },
      take: 1,
    });
    const lastCode = lastUser.length
      ? parseInt(lastUser[0].code.split('-')[1], 10)
      : 0;
    this.code = 'R-' + (lastCode + 1).toString().padStart(6, '0');
  }
}
