import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  OneToMany,
} from 'typeorm';
import { EStatus } from '../../domain/config/enums/status.enum';
import { encodePassword, removeUnicode } from '../../utils/common';
import { AccountEntity } from './account.entity';
import { BaseEntity } from './base.entity';
import { RoleEntity } from './role.entity';

@Entity('user')
@Index(['code'], { where: 'deleted_at IS NULL' })
export class UserEntity extends BaseEntity {
  @Column({ name: 'user_name', type: 'varchar', nullable: true })
  userName: string;

  @Column({ name: 'password', type: 'varchar', nullable: true, select: false })
  password: string;

  @Column({ name: 'email', type: 'varchar' })
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  email: string;

  @Column({ name: 'phone', type: 'varchar' })
  phone: string;

  @Column({ name: 'password_algorithm', default: 'bcrypt' })
  passwordAlgorithm: string;

  @Column({ name: 'salt', type: 'varchar' })
  salt: string;

  @Column({ type: 'varchar', default: EStatus.ACTIVE, nullable: true })
  status: EStatus;

  @Column({ name: 'is_admin', type: 'bool', default: false })
  isAdmin: boolean;

  @Column({ name: 'is_need_otp', type: 'bool', default: false })
  isNeedOtp: boolean;

  @Column({ type: 'boolean', default: false })
  isSuperAdmin: boolean;

  @Column({ type: 'boolean', default: false })
  isMobile: boolean;

  @Column({ type: 'varchar', nullable: true })
  code: string;

  @ManyToMany(() => RoleEntity, (role) => role.users)
  @JoinTable({
    name: 'user_roles',
    joinColumns: [{ name: 'user_id', referencedColumnName: 'id' }],
    inverseJoinColumns: [{ name: 'role_id', referencedColumnName: 'id' }],
  })
  roles: RoleEntity[];

  @OneToMany(() => AccountEntity, (account) => account.user) // Define children explicitly
  accounts: AccountEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  algorithmPassword() {
    if (this.password) {
      const encode = encodePassword(
        this.password,
        null,
        this.passwordAlgorithm ?? 'bcrypt',
      );
      this.salt = '';
      this.password = encode.encoded;
    }
  }

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue = removeUnicode(this.email)?.trim();
  }

  @BeforeInsert()
  async generateCode() {
    const lastUser = await UserEntity.find({
      select: { code: true },
      order: { createdAt: 'DESC' },
    });
    const lastCode = lastUser.length ? parseInt(lastUser[0].code, 10) : 0;
    this.code = (lastCode + 1).toString().padStart(6, '0');
  }
}
