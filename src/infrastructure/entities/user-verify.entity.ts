import { AfterLoad, Column, <PERSON>tity, PrimaryColumn, Unique } from 'typeorm';
import { BaseEntity } from './base.entity';
import { OmitType } from '@nestjs/swagger';
import { ETokenType } from '../../domain/config/enums/token-type.enum';

@Entity({ name: 'user_verify' })
export class UserVerifyEntity extends OmitType(BaseEntity, ['id']) {
  @PrimaryColumn({ name: 'user_id', type: 'uuid', nullable: false })
  userId: string;

  @PrimaryColumn({
    name: 'token_type',
    type: 'varchar',
    nullable: false,
    enum: ETokenType,
  })
  tokenType: string;

  @Column({ name: 'otp', type: 'varchar', nullable: false })
  otp: string;

  @Column({ name: 'token', type: 'varchar', nullable: false })
  token: string;

  @Column({ name: 'expire_otp_time', type: 'bigint', nullable: false })
  expireOtpTime: number;

  @Column({
    name: 'count_otp_time',
    type: 'bigint',
    nullable: false,
    default: 1,
  })
  countOtpTime: number;

  @Column({ name: 'send_otp_time', type: 'bigint', nullable: false })
  sendOtpTime: number;

  @AfterLoad()
  updateCountOtp() {
    const countOtpTime = Number(this.countOtpTime);
    this.countOtpTime = countOtpTime;

    const expireOtpTime = Number(this.expireOtpTime);
    this.expireOtpTime = expireOtpTime;

    const sendOtpTime = Number(this.sendOtpTime);
    this.sendOtpTime = sendOtpTime;
  }
}
