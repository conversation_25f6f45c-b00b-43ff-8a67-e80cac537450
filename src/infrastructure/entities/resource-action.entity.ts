import {
  BeforeInse<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { RolePermissionEntity } from './role-permission.entity';
import { ResourceEntity } from './resource.entity';

@Entity('resource_actions')
export class ResourceActionEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  actionAliases: string;

  @Column({ type: 'varchar' })
  description: string;

  @Column({ type: 'uuid' })
  resourceId: string;

  @Column({ type: 'uuid', nullable: true })
  resourceIdGroup: string;

  @Column({ type: 'boolean', default: true })
  isEnabled: boolean;

  @ManyToOne(() => ResourceEntity, (resource) => resource.resourceActions) // Inverted relationship for clarity
  resource: ResourceEntity | null;

  @ManyToOne(() => ResourceEntity, (resource) => resource.resourceGroupActions) // Inverted relationship for clarity
  @JoinColumn([{ name: 'resource_id_group', referencedColumnName: 'id' }])
  resourceGroup: ResourceEntity | null;

  @OneToMany(
    () => RolePermissionEntity,
    (rolePermission) => rolePermission.resource,
  ) // Define children explicitly
  rolePermissions: RolePermissionEntity[];

  @BeforeInsert()
  convertSearchValue() {
    this.searchValue = removeUnicode(this.description);
  }
}
