import { OmitType } from '@nestjs/swagger';
import {
  AfterInsert,
  AfterLoad,
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
} from 'typeorm';
import {
  EFileImportStatus,
  EFileImportType,
} from '../../domain/config/enums/file-import-history.enum';
import { BaseEntity } from './base.entity';

@Entity('identity-file-import-histories')
export class FileImportHistoryEntity extends BaseEntity {
  @Column({ name: 'file_name', type: 'varchar', nullable: false })
  fileName: string; //Mô tả

  @Column({ name: 'file_path', type: 'varchar', nullable: false })
  filePath: string; //<PERSON>ô tả

  @Column({
    name: 'status',
    type: 'enum',
    nullable: true,
    enum: EFileImportStatus,
    default: EFileImportStatus.WAITING,
  })
  status: EFileImportStatus; //Trạng thái

  @Column({
    name: 'import_type',
    type: 'enum',
    nullable: false,
    enum: EFileImportType,
  })
  importType: EFileImportType; //Loại import

  @Column({ name: 'errors', type: 'varchar', nullable: true })
  errors: string | object[]; //Người thực hiện

  @BeforeInsert()
  @BeforeUpdate()
  jsonStringify() {
    if (this.createdBy && typeof this.createdBy !== 'string') {
      this.createdBy = JSON.stringify(this.createdBy);
    }
    if (this.errors && typeof this.errors !== 'string') {
      this.errors = JSON.stringify(this.errors);
    }
  }

  @AfterLoad()
  @AfterInsert()
  parseJsonString() {
    if (this.createdBy && typeof this.createdBy === 'string') {
      this.createdBy = JSON.parse(this.createdBy);
    }
    if (this.errors && typeof this.errors === 'string') {
      this.errors = JSON.parse(this.errors);
    }
  }
}
