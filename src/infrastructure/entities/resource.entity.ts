import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { removeUnicode } from '../../utils/common';
import { BaseEntity } from './base.entity';
import { ResourceActionEntity } from './resource-action.entity';
import { RolePermissionEntity } from './role-permission.entity';
import { EPortal } from '../../domain/config/enums/portal.enum';

@Entity('resources')
export class ResourceEntity extends BaseEntity {
  @Column({ type: 'varchar' })
  resourceAliases: string;

  @Column({ type: 'varchar' })
  description: string;

  @Column({ type: 'uuid', nullable: true })
  parentId: string;

  @Column({ type: 'boolean', default: true })
  isEnabled: boolean;

  @Column({
    enum: Object.values(EPlatform),
    default: EPlatform.E_PURCHASE,
    nullable: true,
  })
  platform: EPlatform;

  @Column({
    enum: Object.values(EPortal),
    default: EPortal.WEB_ADMIN,
    nullable: true,
  })
  portal: EPortal;

  @Column({ type: 'int', default: 0 })
  resourceOrder: number;

  @ManyToOne(() => ResourceEntity, (resource) => resource.children) // Inverted relationship for clarity
  parent: ResourceEntity | null;

  @OneToMany(() => ResourceEntity, (role) => role.parent) // Define children explicitly
  children: ResourceEntity[];

  @OneToMany(
    () => RolePermissionEntity,
    (rolePermission) => rolePermission.resource,
  ) // Define children explicitly
  rolePermissions: RolePermissionEntity[];

  @OneToMany(
    () => ResourceActionEntity,
    (resourceAction) => resourceAction.resourceGroup,
  ) // Define children explicitly
  resourceActions: ResourceActionEntity[];

  @OneToMany(
    () => ResourceActionEntity,
    (resourceAction) => resourceAction.resource,
  ) // Define children explicitly
  resourceGroupActions: ResourceActionEntity[];

  @BeforeInsert()
  @BeforeUpdate()
  convertSearchValue() {
    this.searchValue = removeUnicode(this.description);
  }
}
