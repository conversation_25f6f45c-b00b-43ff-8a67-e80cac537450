import { Column, Entity, ManyToOne } from 'typeorm';
import { BaseEntity } from './base.entity';
import { DataRoleEntity } from './data-role.entity';
import { RoleEntity } from './role.entity';

@Entity('data_permissions')
export class DataPermissionEntity extends BaseEntity {
  @Column({ type: 'uuid' })
  roleId: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  isPersonal: boolean;

  @Column({ type: 'uuid', nullable: true })
  dataRoleId: string;

  @Column({ type: 'boolean', default: false, nullable: true })
  allowView: boolean;

  @ManyToOne(() => RoleEntity, (role) => role.dataPermissions) // Inverted relationship for clarity
  role: RoleEntity | null;

  @ManyToOne(() => DataRoleEntity, (dataRole) => dataRole.dataPermissions, {
    nullable: true,
  }) // Inverted relationship for clarity
  dataRole: DataRoleEntity | null;
}
