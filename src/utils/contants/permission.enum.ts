import { EAquaStaffPermission } from './aqua-permission.enum';

export enum EUserPermission {
  VIEW = 'user:view',
  CREATE = 'user:create',
  EDIT = 'user:edit',
  DELETE = 'user:delete',
  IMPORT = 'user:import:execute',
}

export enum ERolePermission {
  VIEW = 'role:view',
  CREATE = 'role:create',
  EDIT = 'role:edit',
  DELETE = 'role:delete',
}

export enum EAdmin {
  ADMIN_SUPER = 'is_super_admin',
  ADMIN = 'is_admin',
}

export enum EBusinessOwnerPermission {
  VIEW = 'bo:view',
  CREATE = 'bo:create',
  EDIT = 'bo:edit',
  DELETE = 'bo:delete',
  IMPORT = 'bo:import:execute',
  EXPORT = 'bo:export:execute',
}

export enum EFunctionUnitPermission {
  VIEW = 'function_unit:view',
  CREATE = 'function_unit:create',
  EDIT = 'function_unit:edit',
  DELETE = 'function_unit:delete',
  IMPORT = 'function_unit:import:execute',
  EXPORT = 'function_unit:export:execute',
}

export enum EDepartmentPermission {
  VIEW = 'department:view',
  CREATE = 'department:create',
  EDIT = 'department:edit',
  DELETE = 'department:delete',
  IMPORT = 'department:import:execute',
  EXPORT = 'department:export:execute',
}

export enum EBusinessUnitPermission {
  VIEW = 'business_unit:view',
  CREATE = 'business_unit:create',
  EDIT = 'business_unit:edit',
  DELETE = 'business_unit:delete',
  IMPORT = 'business_unit:import:execute',
  EXPORT = 'business_unit:export:execute',
}

export enum ECompanyPermission {
  VIEW = 'company:view',
  CREATE = 'company:create',
  EDIT = 'company:edit',
  DELETE = 'company:delete',
  IMPORT = 'company:import:execute',
  EXPORT = 'company:export:execute',
}

export enum ESectorPermission {
  VIEW = 'sector:view',
  CREATE = 'sector:create',
  EDIT = 'sector:edit',
  DELETE = 'sector:delete',
  IMPORT = 'sector:import:execute',
  EXPORT = 'sector:export:execute',
}

export enum ESupplierPermission {
  VIEW = 'supplier:view',
  CREATE = 'supplier:create',
  EDIT = 'supplier:edit',
  DELETE = 'supplier:delete',
  IMPORT = 'supplier:import:execute',
  EXPORT = 'supplier:export:execute',
}

export enum EPurchasingGroupPermission {
  VIEW = 'purchasing_group:view',
  CREATE = 'purchasing_group:create',
  EDIT = 'purchasing_group:edit',
  DELETE = 'purchasing_group:delete',
  IMPORT = 'purchasing_group:import:execute',
  EXPORT = 'purchasing_group:export:execute',
}

export enum EPurchasingDepartmentPermission {
  VIEW = 'purchasing_department:view',
  CREATE = 'purchasing_department:create',
  EDIT = 'purchasing_department:edit',
  DELETE = 'purchasing_department:delete',
  IMPORT = 'purchasing_department:import:execute',
  EXPORT = 'purchasing_department:export:execute',
}

export enum EPlantPermission {
  VIEW = 'plant:view',
  CREATE = 'plant:create',
  EDIT = 'plant:edit',
  DELETE = 'plant:delete',
  IMPORT = 'plant:import:execute',
  EXPORT = 'plant:export:execute',
}

export enum EMaterialTypePermission {
  VIEW = 'material_type:view',
  CREATE = 'material_type:create',
  EDIT = 'material_type:edit',
  DELETE = 'material_type:delete',
  IMPORT = 'material_type:import:execute',
  EXPORT = 'material_type:export:execute',
}

export enum EMaterialGroupPermission {
  VIEW = 'material_group:view',
  CREATE = 'material_group:create',
  EDIT = 'material_group:edit',
  DELETE = 'material_group:delete',
  IMPORT = 'material_group:import:execute',
  EXPORT = 'material_group:export:execute',
}

export enum EMaterialPermission {
  VIEW = 'material:view',
  CREATE = 'material:create',
  EDIT = 'material:edit',
  DELETE = 'material:delete',
  IMPORT = 'material:import:execute',
  EXPORT = 'material:export:execute',
}

export enum EPositionPermission {
  VIEW = 'position:view',
  CREATE = 'position:create',
  EDIT = 'position:edit',
  DELETE = 'position:delete',
  IMPORT = 'position:import:execute',
  EXPORT = 'position:export:execute',
}

export enum EStaffPermission {
  VIEW = 'staff:view',
  CREATE = 'staff:create',
  EDIT = 'staff:edit',
  DELETE = 'staff:delete',
  IMPORT = 'staff:import:execute',
  EXPORT = 'staff:export:execute',
}

export enum ECurrencyUnitPermission {
  VIEW = 'currency_unit:view',
  CREATE = 'currency_unit:create',
  EDIT = 'currency_unit:edit',
  DELETE = 'currency_unit:delete',
  IMPORT = 'currency_unit:import:execute',
  EXPORT = 'currency_unit:export:execute',
}

/* PR: Purchase Request */
export enum EAdminPrPermission {
  VIEW = 'pr_ad:view',
  CREATE = 'pr_ad:create',
  EDIT = 'pr_ad:edit',
  DELETE = 'pr_ad:delete',
}
/* IP: Internal Portal */
export enum EIpPrPermission {
  VIEW = 'pr_ip:view',
  CREATE = 'pr_ip:create',
  EDIT = 'pr_ip:edit',
  DELETE = 'pr_ip:delete',
}

/* PIR: Price Information Record */
export enum EAdminPirPermission {
  VIEW = 'pir_ad:view',
  CREATE = 'pir_ad:create',
  EDIT = 'pir_ad:edit',
  DELETE = 'pir_ad:delete',
  IMPORT = 'pir_ad:import:execute',
}

export enum EIpPirPermission {
  VIEW = 'pir_ip:view',
  CREATE = 'pir_ip:create',
  EDIT = 'pir_ip:edit',
  DELETE = 'pir_ip:delete',
  IMPORT = 'pir_ip:import:execute',
}

/* PO: Purchase Order */
export enum EAdminPoPermission {
  VIEW = 'po_ad:view',
  CREATE = 'po_ad:create',
  EDIT = 'po_ad:edit',
  DELETE = 'po_ad:delete',
}

export enum EIpPoPermission {
  VIEW = 'po_ip:view',
  CREATE = 'po_ip:create',
  EDIT = 'po_ip:edit',
  DELETE = 'po_ip:delete',
}

export enum EPurchaseRequestTypePermission {
  VIEW = 'purchase_request_type:view',
  CREATE = 'purchase_request_type:create',
  EDIT = 'purchase_request_type:edit',
  DELETE = 'purchase_request_type:delete',
  IMPORT = 'purchase_request_type:import:execute',
  EXPORT = 'purchase_request_type:export:execute',
}

export enum EPurchaseOrderTypePermission {
  VIEW = 'purchase_order_type:view',
  CREATE = 'purchase_order_type:create',
  EDIT = 'purchase_order_type:edit',
  DELETE = 'purchase_order_type:delete',
  IMPORT = 'purchase_order_type:import:execute',
  EXPORT = 'purchase_order_type:export:execute',
}

export enum ECustomerPermission {
  VIEW = 'customer:view',
  CREATE = 'customer:create',
  EDIT = 'customer:edit',
  DELETE = 'customer:delete',
  IMPORT = 'customer:import:execute',
}

export enum EPopPermission {
  POP_FEED_VIEW = 'pf:view',
  POP_FEED_CREATE = 'pf:create',
  POP_FEED_EDIT = 'pf:edit',
  POP_FEED_DELETE = 'pf:delete',
  POP_FEED_IMPORT = 'pf:import:execute',
  POP_FEED_EXPORT = 'pf:export:execute',
  POP_FEED_RETURN = 'pf:return:execute',
  POP_FEED_CONFIRM = 'pf:confirm:execute',
  POP_FEED_OVERVIEW = 'pf:overview:execute',
  POP_AQUA_VIEW = 'pa:view',
  POP_AQUA_CREATE = 'pa:create',
  POP_AQUA_EDIT = 'pa:edit',
  POP_AQUA_DELETE = 'pa:delete',
  POP_AQUA_IMPORT = 'pa:import:execute',
  POP_AQUA_EXPORT = 'pa:export:execute',
  POP_AQUA_RETURN = 'pa:return:execute',
  POP_AQUA_CONFIRM = 'pa:confirm:execute',
  POP_AQUA_OVERVIEW = 'pa:overview:execute',
}

export enum EWeeklyOrderPlantPermission {
  WOP_FEED_VIEW = 'wopf:view',
  WOP_FEED_CREATE = 'wopf:create',
  WOP_FEED_EDIT = 'wopf:edit',
  WOP_FEED_DELETE = 'wopf:delete',
  WOP_FEED_IMPORT = 'wopf:import:execute',
  WOP_FEED_EXPORT = 'wopf:export:execute',
  WOP_FEED_RETURN = 'wopf:return:execute',
  WOP_FEED_CONFIRM = 'wopf:confirm:execute',
  WOP_FEED_OVERVIEW = 'wopf:overview:execute',
  WOP_AQUA_VIEW = 'wopa:view',
  WOP_AQUA_CREATE = 'wopa:create',
  WOP_AQUA_EDIT = 'wopa:edit',
  WOP_AQUA_DELETE = 'wopa:delete',
  WOP_AQUA_IMPORT = 'wopa:import:execute',
  WOP_AQUA_EXPORT = 'wopa:export:execute',
  WOP_AQUA_RETURN = 'wopa:return:execute',
  WOP_AQUA_CONFIRM = 'wopa:confirm:execute',
  WOP_AQUA_OVERVIEW = 'wopa:overview:execute',
}

export enum EMarketSharePermission {
  MS_FEED_VIEW = 'msf:view',
  MS_FEED_CREATE = 'msf:create',
  MS_FEED_EDIT = 'msf:edit',
  MS_FEED_DELETE = 'msf:delete',
  MS_FEED_IMPORT = 'msf:import:execute',
  MS_FEED_EXPORT = 'msf:export:execute',
  MS_FEED_RETURN = 'msf:return:execute',
  MS_FEED_CONFIRM = 'msf:confirm:execute',
  MS_FEED_OVERVIEW = 'msf:overview:execute',
  MS_AQUA_VIEW = 'msa:view',
  MS_AQUA_CREATE = 'msa:create',
  MS_AQUA_EDIT = 'msa:edit',
  MS_AQUA_DELETE = 'msa:delete',
  MS_AQUA_IMPORT = 'msa:import:execute',
  MS_AQUA_EXPORT = 'msa:export:execute',
  MS_AQUA_RETURN = 'msa:return:execute',
  MS_AQUA_CONFIRM = 'msa:confirm:execute',
  MS_AQUA_OVERVIEW = 'msa:overview:execute',
}

export enum EInventoryStandardPermission {
  VIEW = 'is:view',
  CREATE = 'is:create',
  EDIT = 'is:edit',
  DELETE = 'is:delete',
  IMPORT = 'is:import:execute',
  EXPORT = 'is:export:execute',
}

export enum ENotificationFormPermission {
  VIEW = 'notif:view',
  CREATE = 'notif:create',
  EDIT = 'notif:edit',
  DELETE = 'notif:delete',
  IMPORT = 'notif:import:execute',
  EXPORT = 'notif:export:execute',
}

export enum EProfitCenterPermission {
  VIEW = 'pc:view',
  CREATE = 'pc:create',
  EDIT = 'pc:edit',
  DELETE = 'pc:delete',
  IMPORT = 'pc:import:execute',
  EXPORT = 'pc:export:execute',
}

export enum ECostPermission {
  VIEW = 'cost:view',
  CREATE = 'cost:create',
  EDIT = 'cost:edit',
  DELETE = 'cost:delete',
  IMPORT = 'cost:import:execute',
  EXPORT = 'cost:export:execute',
}

export type TPermission =
  | EUserPermission
  | ERolePermission
  | EAdmin
  | EBusinessOwnerPermission
  | EFunctionUnitPermission
  | EDepartmentPermission
  | EBusinessUnitPermission
  | ECompanyPermission
  | ESectorPermission
  | ESupplierPermission
  | EPurchasingGroupPermission
  | EPurchasingDepartmentPermission
  | EPlantPermission
  | EMaterialTypePermission
  | EMaterialGroupPermission
  | EMaterialPermission
  | EPositionPermission
  | EStaffPermission
  | ECurrencyUnitPermission
  | EAdminPrPermission
  | EIpPrPermission
  | EAdminPirPermission
  | EIpPirPermission
  | EAdminPoPermission
  | EIpPoPermission
  | EPurchaseRequestTypePermission
  | EPurchaseOrderTypePermission
  | ECustomerPermission
  | EPopPermission
  | EWeeklyOrderPlantPermission
  | EMarketSharePermission
  | EInventoryStandardPermission
  | ENotificationFormPermission
  | EProfitCenterPermission
  | ECostPermission
  | EAquaStaffPermission;
