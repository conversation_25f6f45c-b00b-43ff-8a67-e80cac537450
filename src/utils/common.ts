import * as moment from 'moment';
import * as bcrypt from 'bcryptjs';
import * as crypto from 'crypto';
import { characterNumber } from '../domain/config/contants';
import { Row, ValueType } from 'exceljs';
export const removeUnicode = (str: string = ''): string => {
  if (!str) {
    return '';
  }
  str = str.toLowerCase();
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
  str = str.replace(/đ/g, 'd');
  str = str.replace(
    /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\-|\'| |\"|\&|\#|\[|\]|~|$|_/g,
    '',
  );
  str = str.replace(/-+-/g, ''); //thay thế 2- thành 1-
  str = str.replace(/^\-+|\-+$/g, '');
  return str;
};

export const calculateSkip = (pageNumber: number, pageSize: number) =>
  (pageNumber - 1) * pageSize;

export const regexDateString =
  /^\d{4}(-)(((0)[0-9])|((1)[0-2]))(-)([0-2][0-9]|(3)[0-1])$/i;

export const randomId = (length: number) => {
  let result = '';
  const characters = '0123456789';
  const charactersLength = characters.length;
  let counter = 0;
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
    counter += 1;
  }
  return result;
};

export const isValidDate = (dateString: string) => {
  const date = moment(dateString, 'DD/MM/YYYY', true);
  return date.isValid();
};

export const isNumeric = (str: string) => {
  if (typeof str != 'string') return false; // we only process strings!
  return (
    !isNaN(Number(str)) && // use type coercion to parse the _entirety_ of the string (`parseFloat` alone does not do this)...
    !isNaN(parseFloat(str))
  ); // ...and ensure strings of whitespace fail
};

export const groupByProperty = (arr: any, property: string) => {
  const items = arr.reduce(function (memo: any, x: any) {
    if (!memo[x[property]]) {
      memo[x[property]] = [];
    }
    memo[x[property]].push(x);
    return memo;
  }, {});
  return items;
};

export const caculatePicDate = (dob: Date) => {
  const picDate = new Date(process.env.PIC_DATE || '1993-08-22').getTime();
  const dobDate = new Date(dob).getTime();
  const picCalulate = ((dobDate - picDate) / (1000 * 3600 * 24)) % 1000;
  return picCalulate > 0 ? Math.round(picCalulate) : 0;
};

export const caculateWeekAge = (dob: Date) => {
  const cunrentDate = new Date().getTime();
  const dobDate = new Date(dob).getTime();
  const picCalulate = (cunrentDate - dobDate) / (1000 * 3600 * 24 * 7);
  const temp = (cunrentDate - dobDate) % (1000 * 3600 * 24 * 7);
  return temp > 0 ? Math.floor(picCalulate) + 1 : Math.floor(picCalulate);
};

export const removeElement = (array: string[], elem: string): any => {
  return array.filter((array) => array !== elem);
};
const sha256 = (password: string, salt: string) => {
  const hash = crypto.createHmac('sha256', salt);
  hash.update(password);
  const value = hash.digest('hex');
  return {
    salt: salt,
    passwordHash: value,
  };
};
export function encodePassword(
  origin_password: string,
  salt: string,
  algorithm: string,
) {
  let encoded = '';
  switch (algorithm) {
    case 'sha256':
      if (salt == null)
        salt = crypto
          .randomBytes(Math.ceil(10 / 2))
          .toString('hex')
          .slice(0, 10);
      encoded = sha256(origin_password, salt).passwordHash;
      break;
    case 'bcrypt':
    default:
      encoded = salt
        ? bcrypt.hashSync(origin_password, salt)
        : bcrypt.hashSync(origin_password, 10);
  }
  return { encoded: encoded, salt: salt };
}

export function genOtpCode(length: number = 6) {
  let result = '';
  const charactersLength = characterNumber.length;
  for (let i = 0; i < length; i++) {
    result += characterNumber.charAt(
      Math.floor(Math.random() * charactersLength),
    );
  }
  return result;
}

export function uidToPath(uid4) {
  return uid4.replaceAll(/-/g, '');
}

export function merge(target, ...sources) {
  for (const source of sources) {
    mergeValue(target, source);
  }

  return target;

  function innerMerge(target, source) {
    for (const [key, value] of Object.entries(source)) {
      target[key] = mergeValue(target[key], value);
    }
  }

  function mergeValue(targetValue, value) {
    if (Array.isArray(value)) {
      if (!Array.isArray(targetValue)) {
        return [...value];
      } else {
        for (let i = 0, l = value.length; i < l; i++) {
          targetValue[i] = mergeValue(targetValue[i], value[i]);
        }
        return targetValue;
      }
    } else if (typeof value === 'object') {
      if (targetValue && typeof targetValue === 'object') {
        innerMerge(targetValue, value);
        return targetValue;
      } else {
        return value ? { ...value } : value;
      }
    } else {
      return value ?? targetValue ?? undefined;
    }
  }
}

export const generatePassword = (length: number = 8): string => {
  const lowerChars = 'abcdefghijklmnopqrstuvwxyz';
  const upperChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const specialChars = '!@#$%^&*()_+[]{}|;:,.<>?';

  const allChars = lowerChars + upperChars + numbers + specialChars;

  // Ensure the password contains at least one of each character type
  const getRandomChar = (chars: string) =>
    chars[Math.floor(Math.random() * chars.length)];

  let password = '';
  password += getRandomChar(lowerChars);
  password += getRandomChar(upperChars);
  password += getRandomChar(numbers);
  password += getRandomChar(specialChars);

  // Fill the rest of the password length with random characters from all types
  for (let i = 4; i < length; i++) {
    password += getRandomChar(allChars);
  }

  // Shuffle the password to make it more random
  return password
    .split('')
    .sort(() => 0.5 - Math.random())
    .join('');
};

export const getValueOrResult = (cell: Row, col, isNumber = false) => {
  const targetCell = cell.getCell(col);
  if (targetCell.value instanceof Date) {
    return targetCell.value;
  }
  if (targetCell.type == ValueType.Hyperlink) {
    return targetCell.value?.['text'];
  }

  const value =
    typeof targetCell.value == 'object'
      ? targetCell.result
      : targetCell.value?.toString()?.trim();
  return isNumber ? +value : value;
};
