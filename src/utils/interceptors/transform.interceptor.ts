import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { catchError, map, Observable, tap, throwError } from 'rxjs';
import { MessageResponse } from '../../domain/messages/message.response';
import { AxiosError } from 'axios';
import { LoginLogUsecases } from '../../usecases/login-log.usecase';

@Injectable()
export class TransformationInterceptor<T>
  implements NestInterceptor<T, MessageResponse<T>>
{
  constructor(private readonly loginLogUsecases: LoginLogUsecases) {}
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<MessageResponse<T>> {
    const now = Date.now();
    const request = context.switchToHttp().getRequest();

    return next.handle().pipe(
      map((data) => {
        if (Array.isArray(data)) {
          return {
            data,
            message: 'SUCCESSFULLY',
            duration: `${Date.now() - now}ms`,
          };
        } else {
          return {
            data,
            message: data?.message ? data?.message : null,
            duration: `${Date.now() - now}ms`,
          };
        }
      }),
      tap(async (data) => {
        if (request.url.includes('login')) {
          await this.loginLogUsecases.create({
            userId: data?.data?.userId,
            userName: request.body?.userName,
            firstName: data?.data?.firstName,
            lastName: data?.data?.lastName,
            email: data?.data?.email,
            ip: request?.ip,
            message: 'SUCCESSFULLY',
            platform: data?.data?.platform,
            portal: data?.data?.portal,
          });
        }
      }),
      catchError((error) => {
        let message = 'Internal Server Error';
        let errorCode = '0000';
        let detail;
        let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;

        if (error instanceof HttpException) {
          message = error.getResponse()['message'] || message;
          errorCode = error.getResponse()['errorCode'] || errorCode;
          statusCode = error.getStatus();
          detail = error.getResponse()['detail'] || detail;
        } else if (error instanceof AxiosError) {
          message = error.response.data.errors[0].message || message;

          statusCode = error.response.data.statusCode || statusCode;
        } else {
          message = error.message || message;
        }

        return throwError(
          () =>
            new HttpException(
              { message, duration: `${Date.now() - now}ms`, errorCode, detail },
              statusCode,
            ),
        );
      }),
    );
  }
}
