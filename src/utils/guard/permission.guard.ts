import { CanActivate, ExecutionContext, mixin, Type } from '@nestjs/common';
import { EPortal } from '../../domain/config/enums/portal.enum';
import { EAdmin, TPermission } from '../contants/permission.enum';

const NewPermissionGuard = (
  permissions: (TPermission | TPermission[])[],
): Type<CanActivate> => {
  class PermissionGuardMixin {
    async canActivate(context: ExecutionContext) {
      const request = context.switchToHttp().getRequest();
      const token = request.headers['authorization']
        ? request.headers['authorization']
        : null;
      if (token) {
        const base64Payload = token.split('.')[1];
        const payloadBuffer = Buffer.from(base64Payload, 'base64');
        const updatedJwtPayload = JSON.parse(payloadBuffer.toString());
        // user with admin or super_admin role can pass-by the guard
        if (updatedJwtPayload.isSuperAdmin) {
          return true;
        }

        if (
          updatedJwtPayload.portal === EPortal.WEB_ADMIN &&
          permissions.includes(EAdmin.ADMIN)
        ) {
          return true;
        }

        return this.parseScopes(updatedJwtPayload.scopes);
      }
      return false;
    }

    parseScopes(scopes: any[]): boolean {
      if (!scopes || !scopes.length) {
        return false;
      }

      let res = false;
      for (const scope of scopes) {
        const userPermissions: TPermission[] =
          scope.resources?.split(',') || [];

        for (const requirePermission of permissions) {
          let subRes = true;
          if (Array.isArray(requirePermission)) {
            for (const subPermission of requirePermission) {
              subRes = subRes && userPermissions.includes(subPermission);
            }
          } else {
            subRes = userPermissions.includes(requirePermission);
          }
          res = res || subRes;
        }
      }
      return res;
    }
  }

  return mixin(PermissionGuardMixin);
};
export default NewPermissionGuard;
