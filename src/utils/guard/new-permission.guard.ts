import { CanActivate, ExecutionContext, mixin, Type } from '@nestjs/common';
import { EPortal } from '../../domain/config/enums/portal.enum';
import { EAdmin, TPermission } from '../contants/permission.enum';

const NewPermissionGuard = (
  permissions: (TPermission | TPermission[])[],
): Type<CanActivate> => {
  class PermissionGuardMixin {
    async canActivate(context: ExecutionContext) {
      const request = context.switchToHttp().getRequest();
      const jwtPayload = request['jwtPayload'];
      console.log(12, jwtPayload);
      if (!jwtPayload) {
        return false;
      }

      if (jwtPayload.isSuperAdmin) {
        return true;
      }

      if (
        jwtPayload.portal === EPortal.WEB_ADMIN &&
        permissions.includes(EAdmin.ADMIN)
      ) {
        return true;
      }

      return this.parseScopes(jwtPayload.scopes);
    }

    parseScopes(scopes: any[]): boolean {
      if (!scopes || !scopes.length) {
        return false;
      }

      let res = false;
      for (const scope of scopes) {
        const userPermissions: TPermission[] =
          scope.resources?.split(',') || [];

        for (const requirePermission of permissions) {
          let subRes = true;
          if (Array.isArray(requirePermission)) {
            for (const subPermission of requirePermission) {
              subRes = subRes && userPermissions.includes(subPermission);
            }
          } else {
            subRes = userPermissions.includes(requirePermission);
          }
          res = res || subRes;
        }
      }
      return res;
    }
  }

  return mixin(PermissionGuardMixin);
};
export default NewPermissionGuard;
