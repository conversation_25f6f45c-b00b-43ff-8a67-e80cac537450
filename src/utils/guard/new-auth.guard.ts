import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { EPortal } from '../../domain/config/enums/portal.enum';
import { ETokenType } from '../../domain/config/enums/token-type.enum';
import { RoleUsecases } from '../../usecases/role.usecases';
import { RedisUsecases } from '../../usecases/redis.usecases';

@Injectable()
export class NewAuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private roleUsecases: RoleUsecases,
    private readonly redisUsecases: RedisUsecases,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException();
    }
    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
        ignoreExpiration: request.headers['x-api-key'] == process.env.API_KEY,
      });
      if (payload.tokenType != ETokenType.ACCESS) {
        throw new UnauthorizedException();
      }

      if (process.env.ENV == 'PRODUCTION') {
        const platform = request.headers['x-platform'] ?? EPlatform.E_PURCHASE;
        const portal = request.headers['x-portal'] ?? EPortal.WEB_ADMIN;
        //Check platform
        if (payload.platform !== platform) {
          throw new UnauthorizedException();
        }
        //Check portal
        if (payload.portal !== portal) {
          throw new UnauthorizedException();
        }
      }
      let jwtPayload = { ...payload };
      console.log(50, payload);

      // if (payload.userId && payload.platform) {
      //   const scopes = await this.roleUsecases.getScopes(
      //     payload.userId,
      //     payload.platform,
      //   );

      //   const aggregateScopes = this.getScopes(scopes);

      //   jwtPayload = {
      //     ...jwtPayload,
      //     ...aggregateScopes,
      //     scopes: scopes,
      //   };
      // }

      const cachedScopes = await this.redisUsecases.get(token);
      let scopes;

      if (cachedScopes) {
        console.log(73, 'Get scopes from redis');
        console.log(74, JSON.parse(cachedScopes));
        scopes = JSON.parse(cachedScopes);
      } else {
        if (payload.userId && payload.platform) {
          console.log(76, 'Get scopes from db');
          scopes = await this.roleUsecases.getScopes(
            payload.userId,
            payload.platform,
          );

          await this.redisUsecases.set(
            token,
            JSON.stringify(scopes),
            process.env.JWT_EXPIRATION_TIME
              ? Number(process.env.JWT_EXPIRATION_TIME)
              : null,
          );
        }
      }

      const aggregateScopes = this.getScopes(scopes);

      jwtPayload = {
        ...jwtPayload,
        ...aggregateScopes,
        scopes: scopes,
      };
      console.log(65, jwtPayload);
      request['jwtPayload'] = jwtPayload;
    } catch (e) {
      throw new UnauthorizedException();
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }

  private aggregateScopes = (scopes, key) => {
    return scopes
      ?.flatMap((scope) => scope[key]?.split(',') || null)
      .filter(Boolean);
  };

  private getScopes(scopes) {
    const sectors = this.aggregateScopes(scopes, 'sectors');

    const companies = this.aggregateScopes(scopes, 'companies');
    const businessUnits = this.aggregateScopes(scopes, 'businessUnits');
    const departments = this.aggregateScopes(scopes, 'departments');
    const functionUnits = this.aggregateScopes(scopes, 'functionUnits');
    const businessOwners = this.aggregateScopes(scopes, 'businessOwners');
    const materials = this.aggregateScopes(scopes, 'materials');
    const materialGroups = this.aggregateScopes(scopes, 'materialGroups');
    const materialTypes = this.aggregateScopes(scopes, 'materialTypes');
    const positions = this.aggregateScopes(scopes, 'positions');
    const suppliers = this.aggregateScopes(scopes, 'suppliers');
    const purchasingGroups = this.aggregateScopes(scopes, 'purchasingGroups');
    const purchasingDepartments = this.aggregateScopes(
      scopes,
      'purchasingDepartments',
    );
    const plants = this.aggregateScopes(scopes, 'plants');
    const currencyUnits = this.aggregateScopes(scopes, 'currencyUnits');
    const saleOrgs = this.aggregateScopes(scopes, 'saleOrgs');
    const divisions = this.aggregateScopes(scopes, 'divisions');
    const distributionChannels = this.aggregateScopes(
      scopes,
      'distributionChannels',
    );
    const saleGroups = this.aggregateScopes(scopes, 'saleGroups');
    const prTypeCodes = this.aggregateScopes(scopes, 'prTypeCodes');
    const poTypeCodes = this.aggregateScopes(scopes, 'poTypeCodes');
    const costs = this.aggregateScopes(scopes, 'costs');

    return {
      sectors,
      companies,
      businessUnits,
      departments,
      functionUnits,
      businessOwners,
      materials,
      materialGroups,
      materialTypes,
      positions,
      suppliers,
      purchasingGroups,
      purchasingDepartments,
      plants,
      currencyUnits,
      saleOrgs,
      divisions,
      distributionChannels,
      saleGroups,
      prTypeCodes,
      poTypeCodes,
      costs,
    };
  }
}
