import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { EPortal } from '../../domain/config/enums/portal.enum';
import { ETokenType } from '../../domain/config/enums/token-type.enum';

@Injectable()
export class NewAuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    //const res = context.switchToHttp().getResponse();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException();
    }
    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
        ignoreExpiration: request.headers['x-api-key'] == process.env.API_KEY,
      });
      if (payload.tokenType != ETokenType.ACCESS) {
        throw new UnauthorizedException();
      }

      if (process.env.ENV == 'PRODUCTION') {
        const platform = request.headers['x-platform'] ?? EPlatform.E_PURCHASE;
        const portal = request.headers['x-portal'] ?? EPortal.WEB_ADMIN;
        //Check platform
        if (payload.platform !== platform) {
          throw new UnauthorizedException();
        }
        //Check portal
        if (payload.portal !== portal) {
          throw new UnauthorizedException();
        }
      }

      //res.setHeader('foo', payload.userInfo.id || 'test');
      request['userInfo'] = payload;
    } catch (e) {
      console.log(e);
      throw new UnauthorizedException();
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
