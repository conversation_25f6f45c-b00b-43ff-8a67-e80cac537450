import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class CreateMobileManagementDto {
  @ApiProperty({
    type: String,
    description: 'Android version',
  })
  @IsNotEmpty()
  @IsString()
  androidVersion: string;

  @ApiProperty({
    type: String,
    description: 'IOS version',
  })
  @IsNotEmpty()
  @IsString()
  iosVersion: string;

  @ApiProperty({
    type: Bo<PERSON><PERSON>,
    description: '<PERSON><PERSON><PERSON> thị create/delete account mobile',
  })
  @IsNotEmpty()
  @IsBoolean()
  isShowAccountManagement: boolean;

  @ApiProperty({
    type: Bo<PERSON>an,
    description: 'Bắt buộc cập nhật Android',
  })
  @IsNotEmpty()
  @IsBoolean()
  isForceUpdateAndroid: boolean;

  @ApiProperty({
    type: <PERSON><PERSON><PERSON>,
    description: 'Bắt buộc cập nhật Ios',
  })
  @IsNotEmpty()
  @IsBoolean()
  isForceUpdateIos: boolean;

  platform?: EPlatform;
}
