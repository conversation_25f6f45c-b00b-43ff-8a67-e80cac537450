import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { MobileManagementUsecases } from '../../usecases/mobile-management.usecase';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { NewAuthUser } from '../../utils/interceptors/new-user.decorator';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateMobileManagementDto } from './dtos/create-mobile-management.dto';
import { GetDetailMobileManagementDto } from './dtos/get-detail-mobile-management.dto';
import { UpdateMobileManagementDto } from './dtos/update-mobile-management.dto';

@Controller('/mobile-managment')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@ApiTags('Mobile Management')
export class MobileManagementController {
  constructor(
    private readonly mobileManagementUsecases: MobileManagementUsecases,
  ) {}

  @Get('/detail')
  async getDetailMobileManagement(@Query() data: GetDetailMobileManagementDto) {
    return await this.mobileManagementUsecases.getDetailMobileManagement(
      data.platform,
    );
  }

  @Post('/create-mobile-management')
  @UseGuards(NewAuthGuard)
  async createMobile(
    @Body() data: CreateMobileManagementDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.mobileManagementUsecases.createUpdateMobileManagement(
      data,
      jwtPayload,
    );
  }

  @Post('/update-mobile-management')
  @UseGuards(NewAuthGuard)
  async updateMobile(
    @Body() data: UpdateMobileManagementDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.mobileManagementUsecases.createUpdateMobileManagement(
      data,
      jwtPayload,
    );
  }
}
