import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ResourceActionUsecases } from '../../usecases/resource-action.usecases';
import { ResourceUsecases } from '../../usecases/resource.usecases';
import { EAdmin } from '../../utils/contants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateResourceDto } from './dtos/create-resource.dto';
import { GetDetailResourceDto } from './dtos/get-detail-resource.dto';
import { GetResourceListDto } from './dtos/get-resource-list.dto';
import { NewAuthUser } from '../../utils/interceptors/new-user.decorator';
import { IAuthUserPayload } from '../../domain/interface/auth-user-payload.interface';

@Controller('/resource')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard, NewPermissionGuard([EAdmin.ADMIN]))
@ApiTags('Resource')
export class ResourceController {
  constructor(
    private readonly resourceUsecases: ResourceUsecases,
    private readonly resourceActionUsecases: ResourceActionUsecases,
  ) {}

  @Post('/create')
  @UseInterceptors(TransactionInterceptor)
  async create(@Body() data: CreateResourceDto) {
    return await this.resourceUsecases.createResource(data);
  }

  @Get(':id/detail')
  async getDetail(@Param() param: GetDetailResourceDto) {
    return await this.resourceUsecases.getResourceById(param.id);
  }

  @Get('/list')
  async getList(@NewAuthUser() jwtPayload: IAuthUserPayload) {
    return await this.resourceUsecases.getResources(jwtPayload);
  }

  @Delete(':id/delete')
  async delete(@Param() param: GetDetailResourceDto) {
    return await this.resourceUsecases.deleteResource(param.id);
  }

  @Post('/action/create')
  @UseInterceptors(TransactionInterceptor)
  async createResourceAction(@Body() data: CreateResourceDto) {
    return await this.resourceActionUsecases.createResourceAction(data);
  }
}
