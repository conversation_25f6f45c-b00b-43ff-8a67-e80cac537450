import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class CreateResourceDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Alias of resource',
  })
  @IsNotEmpty({ message: 'VALIDATE.RESOURCE_ALIASES.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.RESOURCE_ALIASES.MUST_BE_STRING' })
  resourceAliases: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Description of resource',
  })
  @IsNotEmpty({ message: 'VALIDATE.DESCRIPTION.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description: string;

  @ApiProperty({ type: String, required: false, description: 'Parent ID' })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.PARENT_ID.MUST_BE_UUID' })
  parentId?: string;

  @ApiProperty({ type: Number, required: false, description: 'Parent ID' })
  @IsOptional()
  @IsInt({ message: 'VALIDATE.RESOURCE_ORDER.MUST_BE_NUMBER' })
  resourceOrder?: number;

  @ApiProperty({
    type: EPlatform,
    enum: EPlatform,
    required: true,
    description: 'Platform of resource',
    example: 'web_admin',
  })
  @IsNotEmpty({ message: 'VALIDATE.PLATFORM.IS_REQUIRED' })
  @IsEnum(EPlatform, { message: 'VALIDATE.PLATFORM.MUST_BE_ENUM' })
  platform: EPlatform;
}
