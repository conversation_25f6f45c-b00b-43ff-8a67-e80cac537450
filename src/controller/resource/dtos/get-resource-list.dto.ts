import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { EPortal } from '../../../domain/config/enums/portal.enum';

export class GetResourceListDto extends PartialType(PaginationDto) {
  @ApiProperty({
    type: [EPortal],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EPortal, { each: true })
  portals?: EPortal[];

  @ApiProperty({
    type: EPlatform,
    required: false,
  })
  @IsEnum(EPlatform)
  platform?: EPlatform;
}
