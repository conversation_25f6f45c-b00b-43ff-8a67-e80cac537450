import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class CreateResourceActionDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Description of resource',
  })
  @IsNotEmpty({ message: 'VALIDATE.DESCRIPTION.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description: string;

  @ApiProperty({ type: String, required: true, description: 'Resource ID' })
  @IsNotEmpty({ message: 'VALIDATE.RESOURCE_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.RESOURCE_ID.MUST_BE_UUID' })
  resourceId: string;

  @ApiProperty({ type: String, required: false, description: 'Resource Group' })
  @IsNotEmpty({ message: 'VALIDATE.RESOURCE_ID_GROUP.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.RESOURCE_ID_GROUP.MUST_BE_UUID' })
  resourceIdGroup: string;

  @ApiProperty({
    type: Boolean,
    required: true,
    description: 'Enanbled or not',
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.IS_ENABLED.MUST_BE_BOOLEAN' })
  isEnabled: boolean;
}
