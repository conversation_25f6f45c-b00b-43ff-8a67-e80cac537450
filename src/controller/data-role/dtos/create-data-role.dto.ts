import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { DataRoleType } from '../../../domain/config/enums/data-role-type.enum';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class CreateDataRoleDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Description of Data Role',
  })
  @IsNotEmpty({ message: 'VALIDATE.DESCRIPTION.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  description: string;

  @ApiProperty({ type: String, required: false, description: 'Id of Data' })
  @IsNotEmpty({ message: 'VALIDATE.REF_ID.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_STRING' })
  refId: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Ref code of Data',
  })
  @IsNotEmpty({ message: 'VALIDATE.REF_CODE.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.REF_CODE.MUST_BE_STRING' })
  refCode: string;

  @ApiProperty({
    type: DataRoleType,
    enum: DataRoleType,
    required: true,
    description: 'Type of Data',
  })
  @IsNotEmpty({ message: 'VALIDATE.TYPE.IS_REQUIRED' })
  @IsEnum(DataRoleType, { message: 'VALIDATE.TYPE.MUST_BE_ENUM' })
  type: DataRoleType;

  @ApiProperty({ type: String, required: false, description: 'Parent ID' })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.PARENT_ID.MUST_BE_UUID' })
  parentId?: string;

  @ApiProperty({
    type: EPlatform,
    enum: EPlatform,
    required: false,
    description: 'Platform of Data',
  })
  @IsOptional()
  @IsEnum(EPlatform, { message: 'VALIDATE.TYPE.MUST_BE_ENUM' })
  platform?: EPlatform;
}
