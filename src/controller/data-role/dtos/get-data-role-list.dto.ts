import { ApiProperty } from '@nestjs/swagger';
import { DataRoleType } from '../../../domain/config/enums/data-role-type.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class GetDataRoleListDto extends PaginationDto {
  ids?: string[];

  @ApiProperty({
    type: [DataRoleType],
    isArray: true,
    enum: DataRoleType,
    required: false,
    description: 'Type of Data',
    example: [DataRoleType.COMPANY],
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (!Array.isArray(value)) {
      return value ? [value] : [];
    }
    return value;
  })
  @IsArray()
  @IsEnum(DataRoleType, { each: true })
  types?: DataRoleType[];

  platform?: EPlatform;
}
