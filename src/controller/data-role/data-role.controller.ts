import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { IAuthUserPayload } from '../../domain/interface/auth-user-payload.interface';
import { DataRoleUsecases } from '../../usecases/data-role.usecases';
import {
  EBusinessOwnerPermission,
  EBusinessUnitPermission,
  ECompanyPermission,
  ECostPermission,
  EDepartmentPermission,
  EFunctionUnitPermission,
  EMaterialGroupPermission,
  EMaterialPermission,
  EMaterialTypePermission,
  EPlantPermission,
  EPositionPermission,
  EPurchaseOrderTypePermission,
  EPurchaseRequestTypePermission,
  EPurchasingDepartmentPermission,
  EPurchasingGroupPermission,
  ESectorPermission,
  ESupplierPermission,
} from '../../utils/contants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { NewAuthUser } from '../../utils/interceptors/new-user.decorator';
import { CreateDataRoleDto } from './dtos/create-data-role.dto';
import { GetDataRoleListDto } from './dtos/get-data-role-list.dto';
import { GetDetailDataRoleDto } from './dtos/get-detail-data-role.dto';
import { UpdateDataRoleDto } from './dtos/update-data-role.dto';

@Controller('/data-role')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Data Role')
export class DataRoleController {
  constructor(private readonly dataRoleUsecases: DataRoleUsecases) {}

  @Post('/create')
  @UseGuards(
    NewPermissionGuard([
      EBusinessOwnerPermission.CREATE,
      EFunctionUnitPermission.CREATE,
      EDepartmentPermission.CREATE,
      EBusinessUnitPermission.CREATE,
      ECompanyPermission.CREATE,
      ESectorPermission.CREATE,
      EDepartmentPermission.CREATE,
      ESupplierPermission.CREATE,
      EPurchasingGroupPermission.CREATE,
      EPurchasingDepartmentPermission.CREATE,
      EPlantPermission.CREATE,
      EMaterialTypePermission.CREATE,
      EMaterialGroupPermission.CREATE,
      EMaterialPermission.CREATE,
      EPositionPermission.CREATE,
      EPurchaseRequestTypePermission.CREATE,
      EPurchaseOrderTypePermission.CREATE,
      ECostPermission.CREATE,
    ]),
  )
  async create(@Body() data: CreateDataRoleDto) {
    return await this.dataRoleUsecases.createDataRole(data);
  }

  @Get(':id/detail')
  async getDetail(@Param() param: GetDetailDataRoleDto) {
    return await this.dataRoleUsecases.getDataRoleById(param.id);
  }

  @Get('/list')
  async getList(
    @Query() param: GetDataRoleListDto,
    @NewAuthUser() jwtPayload: IAuthUserPayload,
  ) {
    param.platform = jwtPayload?.platform;
    return await this.dataRoleUsecases.getDataRoles(param);
  }

  @Delete(':refId')
  @UseGuards(
    NewPermissionGuard([
      EBusinessOwnerPermission.DELETE,
      EFunctionUnitPermission.DELETE,
      EDepartmentPermission.DELETE,
      EBusinessUnitPermission.DELETE,
      ECompanyPermission.DELETE,
      ESectorPermission.DELETE,
      EDepartmentPermission.DELETE,
      ESupplierPermission.DELETE,
      EPurchasingGroupPermission.DELETE,
      EPurchasingDepartmentPermission.DELETE,
      EPlantPermission.DELETE,
      EMaterialTypePermission.DELETE,
      EMaterialGroupPermission.DELETE,
      EMaterialPermission.DELETE,
      EPositionPermission.DELETE,
      EPurchaseRequestTypePermission.DELETE,
      EPurchaseOrderTypePermission.DELETE,
      ECostPermission.DELETE,
    ]),
  )
  async delete(@Param('refId') refId: string) {
    return await this.dataRoleUsecases.deleteDataRole(refId);
  }

  @Patch(':refId')
  @UseGuards(
    NewPermissionGuard([
      EBusinessOwnerPermission.EDIT,
      EFunctionUnitPermission.EDIT,
      EDepartmentPermission.EDIT,
      EBusinessUnitPermission.EDIT,
      ECompanyPermission.EDIT,
      ESectorPermission.EDIT,
      EDepartmentPermission.EDIT,
      ESupplierPermission.EDIT,
      EPurchasingGroupPermission.EDIT,
      EPurchasingDepartmentPermission.EDIT,
      EPlantPermission.EDIT,
      EMaterialTypePermission.EDIT,
      EMaterialGroupPermission.EDIT,
      EMaterialPermission.EDIT,
      EPositionPermission.EDIT,
      EPurchaseRequestTypePermission.EDIT,
      EPurchaseOrderTypePermission.EDIT,
      ECostPermission.EDIT,
    ]),
  )
  async update(
    @Param('refId') refId: string,
    @Body() updateDataRoleDto: UpdateDataRoleDto,
  ) {
    return await this.dataRoleUsecases.updateDataRole(refId, updateDataRoleDto);
  }
}
