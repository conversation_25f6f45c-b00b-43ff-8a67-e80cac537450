import {
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  HttpStatus,
  Param,
  ParseFilePipe,
  Patch,
  Post,
  Query,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBasicAuth,
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiTags,
} from '@nestjs/swagger';
import { EPlatform } from '../../domain/config/enums/platform.enum';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { LoginLogUsecases } from '../../usecases/login-log.usecase';
import { UserUsecases } from '../../usecases/user.usecase';
import { EAdmin, EUserPermission } from '../../utils/contants/permission.enum';
import { AuthBasicGuard } from '../../utils/guard/auth-basic.guard';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { NewAuthUser } from '../../utils/interceptors/new-user.decorator';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { GetListLoginLogDto } from '../auth/dtos/get-list-login-log.dto';
import { CreateUserDto } from './dtos/create-user.dto';
import { GetApproverByPositionDto } from './dtos/get-approver-by-position.dto';
import { GetDetailUserDto } from './dtos/get-detail-user.dto';
import { GetUserListDto } from './dtos/get-user-list.dto';
import { UpdateUserDto } from './dtos/update-user.dto';
import { CreateUserMobileDto } from './dtos/create-user-mobile.dto';

@Controller('/user')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiTags('Users')
export class UserController {
  constructor(
    private readonly userUsecases: UserUsecases,
    private readonly loginLogUsecases: LoginLogUsecases,
  ) {}

  @Post('/create')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard, NewPermissionGuard([EUserPermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async create(@Body() data: CreateUserDto, @NewAuthUser() jwtPayload: any) {
    data.isMobile = false;
    return await this.userUsecases.createUser(data, jwtPayload);
  }

  @Patch(':userId/update')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard, NewPermissionGuard([EUserPermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async update(
    @Param() param: GetDetailUserDto,
    @Body() data: UpdateUserDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.userUsecases.updateUser(param.userId, data, jwtPayload);
  }

  @Get(':userId/detail')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard, NewPermissionGuard([EUserPermission.EDIT]))
  async getDetail(
    @Param() param: GetDetailUserDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.userUsecases.getDetailUser(
      param.userId,
      jwtPayload?.platform,
    );
  }

  @Get('/list')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard, NewPermissionGuard([EUserPermission.VIEW]))
  async getList(
    @Query() conditions: GetUserListDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.userUsecases.getUsers(conditions, jwtPayload?.platform);
  }

  @Get('/digisales/list')
  @ApiBasicAuth('BasicAuth')
  @UseGuards(AuthBasicGuard)
  async getDigiSalesList(@Query() conditions: GetUserListDto) {
    return await this.userUsecases.getUsers(conditions, EPlatform.DIGI_SALE);
  }

  @Delete(':userId')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard, NewPermissionGuard([EUserPermission.DELETE]))
  async delete(
    @Param() param: GetDetailUserDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.userUsecases.deleteUser(param.userId, jwtPayload);
  }

  @Post('/import-user')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard, NewPermissionGuard([EUserPermission.DELETE]))
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('importFile'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        importFile: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importUser(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new FileTypeValidator({
            fileType:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        ],
        fileIsRequired: true,
        errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      }),
    )
    importFile,
    @NewAuthUser() jwtPayload: any,
    @Request() req,
  ) {
    return await this.userUsecases.importUser(
      importFile,
      jwtPayload,
      req.headers['authorization'],
    );
  }

  @Get('/login-log')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard, NewPermissionGuard([EAdmin.ADMIN_SUPER]))
  async getListLoginLog(
    @Query() conditions: GetListLoginLogDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.loginLogUsecases.getLoginLogs(conditions, jwtPayload);
  }

  @Get('/approver-by-position')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard)
  async approverByPosition(
    @Query() conditions: GetApproverByPositionDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    conditions.platform = jwtPayload?.platform ?? EPlatform.E_PURCHASE;
    return await this.userUsecases.approverByPosition(conditions);
  }

  ///For Mobile
  @Post('/create-mobile')
  @UseInterceptors(TransactionInterceptor)
  async createMobile(
    @Body() data: CreateUserMobileDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    data.isMobile = true;
    return await this.userUsecases.createUserMobile(data, jwtPayload);
  }

  @Delete(':userId/mobile')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard)
  async deleteUserMobile(
    @Param() param: GetDetailUserDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.userUsecases.deleteUserMobile(param.userId, jwtPayload);
  }
}
