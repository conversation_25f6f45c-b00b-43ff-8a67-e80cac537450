import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  ValidateNested,
} from 'class-validator';
import { EStatus } from '../../../domain/config/enums/status.enum';
import { CreateAccountDto } from '../../account/dtos/create-account.dto';

export class CreateUserDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'User Name',
  })
  @IsNotEmpty({ message: 'VALIDATE.RESOURCE_ALIASES.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.RESOURCE_ALIASES.MUST_BE_STRING' })
  userName: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Last Name',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Phone Number',
  })
  @IsOptional()
  @Matches(/^[0-9]+$/, {
    message: 'VALIDATE.PHONE_NUMBER.MUST_BE_NUMBER',
  })
  phone?: string;

  @ApiProperty({
    type: [String],
    isArray: true,
    required: false,
    description: 'Role IDs',
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { message: 'VALIDATE.ROLE_ID.MUST_BE_UUID', each: true })
  roleIds: string[];

  @ApiProperty({
    type: Boolean,
    required: false,
    description: 'For Super Admin',
    default: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.IS_SUPER_ADMIN.MUST_BE_BOOLEAN' })
  isSuperAdmin: boolean;

  @ApiProperty({
    type: Boolean,
    required: false,
    description: 'For OTP',
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.IS_NEED_OTP.MUST_BE_BOOLEAN' })
  isNeedOtp: boolean;

  @ApiProperty({
    type: [CreateAccountDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateAccountDto)
  accounts?: CreateAccountDto[];

  @ApiProperty({
    type: EStatus,
    enum: EStatus,
    required: false,
    description: 'Status',
    default: EStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EStatus)
  status: EStatus;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Password',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.PASSWORD.MUST_BE_STRING' })
  password?: string;

  isMobile?: boolean;
  code?: string;
  createdAt?: string;
  updatedAt?: string;
}
