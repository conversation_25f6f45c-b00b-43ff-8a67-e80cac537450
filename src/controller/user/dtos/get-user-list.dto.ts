import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';
import { EStatus } from '../../../domain/config/enums/status.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetUserListDto extends PaginationDto {
  @ApiProperty({
    type: [EStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EStatus, { each: true })
  statuses: EStatus[];

  platform?: EPlatform;

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.RESOURCE_ALIASES.MUST_BE_ARRAY' })
  @IsUUID('4', { message: 'VALIDATE.USER_ID.MUST_BE_UUID', each: true })
  roleIds: string[];

  @ApiProperty({
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'VALIDATE.STAFF_IDS.MUST_BE_ARRAY' })
  @IsString({ message: 'VALIDATE.STAFF_ID.MUST_BE_STRING', each: true })
  staffIds?: string[];
}
