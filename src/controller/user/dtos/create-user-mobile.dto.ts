import { ApiProperty, OmitType } from '@nestjs/swagger';
import { CreateUserDto } from './create-user.dto';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
} from 'class-validator';
import { CreateAccountDto } from '../../account/dtos/create-account.dto';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class CreateUserMobileDto extends OmitType(CreateUserDto, [
  'password',
  'accounts',
  'isSuperAdmin',
  'isNeedOtp',
  'roleIds',
  'phone',
]) {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Password',
  })
  @IsNotEmpty({ message: 'VALIDATE.PASSWORD.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.PASSWORD.MUST_BE_STRING' })
  password: string;

  @ApiProperty({
    type: EPlatform,
    enum: EPlatform,
    required: false,
    description: 'Platform',
  })
  @IsNotEmpty({ message: 'VALIDATE.PLATFORM.IS_REQUIRED' })
  @IsEnum(EPlatform)
  platform: EPlatform;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Phone Number',
  })
  @IsOptional()
  @Matches(/^[0-9]+$/, {
    message: 'VALIDATE.PHONE_NUMBER.MUST_BE_NUMBER',
  })
  phone?: string;

  accounts?: CreateAccountDto[];
  isSuperAdmin: boolean = false;
  isNeedOtp: boolean = false;
  roleIds?: string[];
}
