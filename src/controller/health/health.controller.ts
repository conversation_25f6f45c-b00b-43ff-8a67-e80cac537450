import { Controller, Get, UseInterceptors } from '@nestjs/common';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { ApiTags } from '@nestjs/swagger';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';

@Controller('/health')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiTags('Health')
export class HealthController {
  @Get('/')
  async health(): Promise<any> {
    return { message: 'OK!!!' };
  }
}
