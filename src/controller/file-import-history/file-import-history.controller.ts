import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { GetUuidDto } from '../../domain/dtos/get-uuid.dto';
import { FileImportHistoryUsecases } from '../../usecases/file-import-history.usecases';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { GetListFileImportHistoryDto } from './dtos/get-list-file-import-history.dto';
import { UpdateStatusFileImportHistoryDto } from './dtos/update-status-file-import-hsitory.dto';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';

@Controller('/file-import-history')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
// @UseGuards(NewAuthGuard)
@ApiTags('File Import History')
export class FileImportHistoryController {
  constructor(
    private readonly fileImportHistoryUsecases: FileImportHistoryUsecases,
  ) {}

  @Get('/list')
  async getList(@Query() param: GetListFileImportHistoryDto) {
    return await this.fileImportHistoryUsecases.getListFileImportHistory(param);
  }

  @Delete(':id/delete')
  async delete(@Param() param: GetUuidDto) {
    await this.fileImportHistoryUsecases.deleteFileImportHistory(param.id);
    return { message: 'Successfully!!!' };
  }

  @Get(':id/detail')
  async getDetail(@Param() param: GetUuidDto) {
    return await this.fileImportHistoryUsecases.getFileImportHistoryDetail(
      param.id,
    );
  }

  @Put('/update-status/:id')
  async updateBudgetOpex(
    @Body() data: UpdateStatusFileImportHistoryDto,
    @Param('id') id: string,
  ) {
    return await this.fileImportHistoryUsecases.updateFileImportHistory(
      id,
      data,
    );
  }
}
