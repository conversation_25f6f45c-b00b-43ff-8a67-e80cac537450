import { ApiProperty, OmitType } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsEnum } from 'class-validator';
import { EFileImportType } from '../../../domain/config/enums/file-import-history.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetListFileImportHistoryDto extends OmitType(PaginationDto, [
  'from',
  'to',
]) {
  @ApiProperty({
    type: [EFileImportType],
    required: true,
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsEnum(EFileImportType, { each: true })
  importTypes: EFileImportType[];
}
