import {
  Controller,
  Post,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FilesInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';

import { FileUsecases } from '../../usecases/file.usecases';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { buffer } from 'stream/consumers';

@Controller('/file')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@ApiTags('File')
export class FileController {
  constructor(private readonly filesUsecases: FileUsecases) {}

  @Post('/upload-files')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FilesInterceptor('files'))
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
      },
    },
  })
  async upload(@UploadedFiles() files: Array<Express.Multer.File>) {
    const uploadedFiles = await this.filesUsecases.uploadFiles(files, null);
    return uploadedFiles.map((item) => {
      return { ...item, buffer: undefined };
    });
  }
}
