import {
  Body,
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { EFileImportType } from '../../domain/config/enums/file-import-history.enum';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { ImportUsecases } from '../../usecases/import.usecases';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { ImportUserDto } from './dtos/import-user.dto';

@Controller('/import')
@UseInterceptors(TransformationInterceptor, TransactionInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Import')
export class ImportController {
  constructor(private readonly importUsecases: ImportUsecases) {}

  @Post('/import-user')
  async importUser(@Body() body: ImportUserDto) {
    console.log(24, body);
    return await this.importUsecases.import(body, EFileImportType.USER, '');
  }
}
