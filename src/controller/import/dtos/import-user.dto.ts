import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsObject, IsOptional } from 'class-validator';
import { ImportBaseDto } from './import-base.dto';
import { CreateUserDto } from '../../user/dtos/create-user.dto';
import { UpdateUserDto } from '../../user/dtos/update-user.dto';

export class ImportUserDto extends ImportBaseDto {
  @ApiProperty({
    type: [CreateUserDto],
  })
  @IsArray()
  @Type(() => CreateUserDto)
  dataUsers: CreateUserDto[];

  @ApiProperty({
    type: [CreateUserDto],
  })
  @IsArray()
  @Type(() => UpdateUserDto)
  dataUpdateUsers: UpdateUserDto[];

  @ApiProperty({
    type: Object,
  })
  @IsOptional()
  @IsObject()
  userInfo: any;
}
