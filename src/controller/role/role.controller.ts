import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { IAuthUserPayload } from '../../domain/interface/auth-user-payload.interface';
import { TransactionInterceptor } from '../../infrastructure/config/typeorm/tracsaction.interceptor';
import { RoleUsecases } from '../../usecases/role.usecases';
import { EAquaStaffPermission } from '../../utils/contants/aqua-permission.enum';
import {
  ERolePermission,
  EUserPermission,
} from '../../utils/contants/permission.enum';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { NewAuthUser } from '../../utils/interceptors/new-user.decorator';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { CreateRoleDto } from './dtos/create-role.dto';
import { GetDetailRoleDto } from './dtos/get-detail-role.dto';
import { GetRoleListDto } from './dtos/get-role-list.dto';
import { UpdateRoleDto } from './dtos/update-role.dto';

@Controller('/roles')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Roles')
export class RoleController {
  constructor(private readonly roleUsecases: RoleUsecases) {}

  @Post('/create')
  @UseGuards(NewPermissionGuard([ERolePermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async create(
    @Body() data: CreateRoleDto,
    @NewAuthUser() jwtPayload: IAuthUserPayload,
  ) {
    return await this.roleUsecases.createRole(data, jwtPayload);
  }

  @Post()
  @UseGuards(NewPermissionGuard([ERolePermission.CREATE]))
  @UseInterceptors(TransactionInterceptor)
  async createFirstRole(
    @Body() data: CreateRoleDto,
    @NewAuthUser() jwtPayload: IAuthUserPayload,
  ) {
    return await this.roleUsecases.createFirstRole(data, jwtPayload);
  }

  @Patch('/update/:id')
  @UseGuards(NewPermissionGuard([ERolePermission.EDIT]))
  @UseInterceptors(TransactionInterceptor)
  async update(
    @Param('id') id: string,
    @Body() data: UpdateRoleDto,
    @NewAuthUser() jwtPayload: IAuthUserPayload,
  ) {
    return await this.roleUsecases.updateRole(id, data, jwtPayload);
  }

  @Get(':id/detail')
  @UseGuards(NewPermissionGuard([ERolePermission.VIEW]))
  async getDetail(
    @Param() param: GetDetailRoleDto,
    @NewAuthUser() jwtPayload: IAuthUserPayload,
  ) {
    return await this.roleUsecases.getDetailRole(param.id, jwtPayload);
  }

  @Get('/list')
  @UseGuards(NewPermissionGuard([ERolePermission.VIEW]))
  async getList(
    @Query() param: GetRoleListDto,
    @NewAuthUser() jwtPayload: IAuthUserPayload,
  ) {
    return await this.roleUsecases.getRoles(param, jwtPayload?.platform);
  }

  @Delete(':id/delete')
  @UseGuards(NewPermissionGuard([ERolePermission.DELETE]))
  async delete(@Param() param: GetDetailRoleDto) {
    return await this.roleUsecases.deleteRole(param.id);
  }

  @Get('get-all-aqua')
  @UseGuards(NewPermissionGuard([EAquaStaffPermission.VIEW]))
  async getAll() {
    return await this.roleUsecases.getAll();
  }

  @Get('get-for-ddl/:farmId')
  @UseGuards(NewPermissionGuard([EAquaStaffPermission.VIEW]))
  async getRolesForDDL(@Param('farmId') farmId: string) {
    return await this.roleUsecases.getRolesForDDL(farmId);
  }

  @Get('get-by-user/')
  @UseGuards(NewPermissionGuard([EAquaStaffPermission.VIEW]))
  async getByUser(
    @Query('userId') userId: string,
    @NewAuthUser() jwtPayload: IAuthUserPayload,
  ) {
    return await this.roleUsecases.getByUser(userId, jwtPayload);
  }

  @Get('/get-by-codes')
  // @UseGuards(NewPermissionGuard([ERolePermission.VIEW]))
  async getByCodes(
    @Query() param: { codes: string[] },
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.roleUsecases.getRoleByCodes(
      param.codes,
      jwtPayload?.platform,
    );
  }

  @Get('/list-assigned')
  @UseGuards(NewPermissionGuard([ERolePermission.VIEW, EUserPermission.VIEW]))
  async getRolesAssigned(
    @Query() param: GetRoleListDto,
    @NewAuthUser() jwtPayload: IAuthUserPayload,
  ) {
    return await this.roleUsecases.getRoles(
      { ...param, isAssigned: true },
      jwtPayload?.platform,
    );
  }
}
