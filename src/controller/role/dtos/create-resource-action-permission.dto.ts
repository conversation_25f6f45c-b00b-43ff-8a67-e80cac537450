import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

export class CreateResourceActionPermissionDto {
  @ApiProperty({
    type: String,
    required: true,
    description: 'Resource ID of resource action',
  })
  @IsNotEmpty({ message: 'VALIDATE.RESOURCE_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.RESOURCE_ID.MUST_BE_UUID' })
  resourceId: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Resource Action ID',
  })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.RESOURCE_ACTION_ID.MUST_BE_UUID' })
  resourceActionId: string;

  @ApiProperty({
    type: Boolean,
    required: true,
    description: 'Action of permission',
  })
  @IsNotEmpty({ message: 'VALIDATE.ACTION_FULL.IS_REQUIRED' })
  @IsBoolean({ message: 'VALIDATE.ACTION_FULL.MUST_BE_BOOLEAN' })
  actionFull: boolean;
}
