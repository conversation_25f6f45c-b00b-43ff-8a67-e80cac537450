import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsUUID } from 'class-validator';

export class AssignRoleToUserDto {
  @ApiProperty({ type: String, required: true, description: 'Data Role ID' })
  @IsNotEmpty({ message: 'VALIDATE.DATA_ROLE_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.DATA_ROLE_ID.MUST_BE_UUID' })
  userId: string;

  @ApiProperty({
    type: Array,
    required: true,
    description: 'Data Role ID',
  })
  @IsArray({ message: 'VALIDATE.ROLE_IDS.MUST_BE_ARRAY' })
  @ArrayMinSize(1, { message: 'VALIDATE.ROLE_IDS.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.DATA_ROLE_ID.MUST_BE_UUID', each: true })
  roleIds: string[];
}
