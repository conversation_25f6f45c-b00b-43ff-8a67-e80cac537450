import { ApiProperty, OmitType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { CreateResourceActionPermissionDto } from './create-resource-action-permission.dto';
import { CreateRolePermissionDto } from './create-role-permission.dto';

export class CreateResourceRolePermissionDto extends OmitType(
  CreateRolePermissionDto,
  ['roleId'],
) {
  @ApiProperty({
    type: [CreateResourceActionPermissionDto],
    required: false,
    description: 'Resource actions',
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateResourceActionPermissionDto)
  actions: CreateResourceActionPermissionDto[];
}
