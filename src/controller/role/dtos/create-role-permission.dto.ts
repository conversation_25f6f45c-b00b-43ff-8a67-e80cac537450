import { ApiProperty } from '@nestjs/swagger';
import {
  Allow,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsUUID,
} from 'class-validator';

export class CreateRolePermissionDto {
  @ApiProperty({ type: String, required: true, description: 'Role ID' })
  @IsNotEmpty({ message: 'VALIDATE.ROLE_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.ROLE_ID.MUST_BE_UUID' })
  roleId: string;

  @ApiProperty({
    type: Boolean,
    required: true,
    description: 'Action of role',
  })
  @IsOptional()
  @Allow(null)
  @IsBoolean({ message: 'VALIDATE.ACTION_VIEW.MUST_BE_BOOLEAN' })
  actionView: boolean;

  @ApiProperty({
    type: Boolean,
    required: true,
    description: 'Action of role',
  })
  @IsOptional()
  @Allow(null)
  @IsBoolean({ message: 'VALIDATE.ACTION_EDIT.MUST_BE_BOOLEAN' })
  actionEdit: boolean;

  @ApiProperty({
    type: Boolean,
    required: true,
    description: 'Action of role',
  })
  @IsOptional()
  @Allow(null)
  @IsBoolean({ message: 'VALIDATE.ACTION_DELETE.MUST_BE_BOOLEAN' })
  actionDelete: boolean;

  @ApiProperty({
    type: Boolean,
    required: true,
    description: 'Action of role',
  })
  @IsOptional()
  @Allow(null)
  @IsBoolean({ message: 'VALIDATE.ACTION_CREATE.MUST_BE_BOOLEAN' })
  actionCreate: boolean;

  @ApiProperty({ type: String, required: true, description: 'Resource ID' })
  @IsNotEmpty({ message: 'VALIDATE.RESOURCE_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.RESOURCE_ID.MUST_BE_UUID' })
  resourceId: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Resource Action ID',
  })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.RESOURCE_ID.MUST_BE_UUID' })
  resourceActionId: string;
}
