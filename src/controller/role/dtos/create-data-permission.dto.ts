import { ApiProperty } from '@nestjs/swagger';
import {
  Allow,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsUUID,
} from 'class-validator';

export class CreateDataPermissionDto {
  @ApiProperty({
    type: Boolean,
    required: true,
    description: 'Get data rights by individual',
  })
  @IsOptional()
  @Allow(null)
  @IsBoolean({ message: 'VALIDATE.IS_PERSONAL.MUST_BE_BOOLEAN' })
  isPersonal: boolean;

  @ApiProperty({ type: String, required: true, description: 'Data Role ID' })
  @IsNotEmpty({ message: 'VALIDATE.DATA_ROLE_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.DATA_ROLE_ID.MUST_BE_UUID' })
  dataRoleId: string;

  @ApiProperty({
    type: Boolean,
    required: true,
    description: 'Action of role data',
  })
  @IsOptional()
  @Allow(null)
  @IsBoolean({ message: 'VALIDATE.ALLOW_VIEW.MUST_BE_BOOLEAN' })
  allowView: boolean;
}
