import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { EStatus } from '../../../domain/config/enums/status.enum';
import { CreateDataPermissionDto } from './create-data-permission.dto';
import { CreateResourceRolePermissionDto } from './create-resource-role-permission.dto';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class CreateRoleDto {
  @ApiProperty({ type: String, required: true, description: 'Name of Role' })
  @IsNotEmpty({ message: 'VALIDATE.NAME.IS_REQUIRED' })
  @IsString({ message: 'VALIDATE.NAME.MUST_BE_UUID' })
  name: string;

  @ApiProperty({
    type: String,
    required: true,
    description: 'Description of Role',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.DESCRIPTION.MUST_BE_UUID' })
  description: string;

  @ApiProperty({ type: String, required: false, description: 'Parent ID' })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.PARENT_ID.MUST_BE_UUID' })
  parentId?: string;

  @ApiProperty({
    type: Boolean,
    required: false,
    description: 'Inherit',
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.ALLOW_INHERIT.MUST_BE_BOOLEAN' })
  allowInherit: boolean;

  @ApiProperty({
    type: [CreateResourceRolePermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateResourceRolePermissionDto)
  webAdminResources: CreateResourceRolePermissionDto[];

  @ApiProperty({
    type: [CreateResourceRolePermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateResourceRolePermissionDto)
  webInternalResources: CreateResourceRolePermissionDto[];

  @ApiProperty({
    type: Boolean,
    required: true,
    description: 'Get data rights by individual',
  })
  @IsOptional()
  @IsBoolean({ message: 'VALIDATE.IS_PERSONAL.MUST_BE_BOOLEAN' })
  isPersonal: boolean;

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  sectors: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  companies: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  businessUnits: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  departments: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  businessOwners: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  functionUnits: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  suppliers: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  materials: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  materialGroups: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  materialTypes: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  positions: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  plants: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  purchasingGroups: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  purchasingDepartments: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  currencyUnits: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  saleOrgs: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  divisions: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  distributionChannels: CreateDataPermissionDto[];

  @ApiProperty({
    type: [CreateDataPermissionDto],
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateDataPermissionDto)
  saleGroups: CreateDataPermissionDto[];

  @ApiProperty({
    type: EStatus,
    enum: EStatus,
    required: true,
    description: 'Platform of resource',
    example: 'web_admin',
  })
  @IsNotEmpty({ message: 'VALIDATE.STATUS.IS_REQUIRED' })
  @IsEnum(EStatus, { message: 'VALIDATE.STATUS.MUST_BE_ENUM' })
  status: EStatus;

  @ApiProperty({
    type: EPlatform,
    enum: EPlatform,
    required: false,
    description: 'Platform of Data',
  })
  @IsOptional()
  @IsEnum(EPlatform, { message: 'VALIDATE.TYPE.MUST_BE_ENUM' })
  platform?: EPlatform;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Farm ID',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.FARM_ID.MUST_BE_STRING' })
  farmId?: string;
}
