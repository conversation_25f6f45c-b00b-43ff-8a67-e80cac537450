import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';
import { EStatus } from '../../../domain/config/enums/status.enum';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetRoleListDto extends PaginationDto {
  @ApiProperty({
    type: [EStatus],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EStatus, { each: true })
  statuses?: EStatus[];

  @ApiProperty({
    type: [EPlatform],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EPlatform, { each: true })
  platforms?: EPlatform[];

  isAssigned?: boolean;
}
