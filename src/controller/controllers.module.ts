import { Module } from '@nestjs/common';
import { EnvironmentConfigModule } from '../infrastructure/config/environment-config/environment-config.module';
import { RepositoriesModule } from '../infrastructure/repositories/repositories.module';
import { AccountUsecases } from '../usecases/account.usecase';
import { authUsecases } from '../usecases/auth.usecases';
import { DataPermissionUsecases } from '../usecases/data-permission.usecases';
import { DataRoleUsecases } from '../usecases/data-role.usecases';
import { EmailUsecases } from '../usecases/email.usecases';
import { FileImportHistoryUsecases } from '../usecases/file-import-history.usecases';
import { FileUsecases } from '../usecases/file.usecases';
import { ImportUsecases } from '../usecases/import.usecases';
import { LoginLogUsecases } from '../usecases/login-log.usecase';
import { RedisUsecases } from '../usecases/redis.usecases';
import { ResourceActionUsecases } from '../usecases/resource-action.usecases';
import { ResourceUsecases } from '../usecases/resource.usecases';
import { RolePermissionUsecases } from '../usecases/role-permission.usecases';
import { RoleUsecases } from '../usecases/role.usecases';
import { UserVerifyUsecases } from '../usecases/user-verify.usecase';
import { UserUsecases } from '../usecases/user.usecase';
import { AccountController } from './account/account.controller';
import { AuthController } from './auth/auth.controllers';
import { DataRoleController } from './data-role/data-role.controller';
import { FileImportHistoryController } from './file-import-history/file-import-history.controller';
import { FileController } from './file/file.controller';
import { ImportController } from './import/import.controller';
import { ResourceController } from './resource/resource.controller';
import { RoleController } from './role/role.controller';
import { UserController } from './user/user.controller';
import { ApiLogController } from './api-log/api-log.controller';
import { ApiLogUsecases } from '../usecases/api-log.usecase';
import { MobileManagementController } from './mobile-management/mobile-management.controller';
import { MobileManagementUsecases } from '../usecases/mobile-management.usecase';
import { SocketService } from '../infrastructure/config/socket/socket.service';
import { SocketGateway } from '../infrastructure/config/socket/socket.gateway';
import { ChannelsService } from '../infrastructure/config/socket/channels.service';
import { HealthController } from './health/health.controller';

@Module({
  imports: [RepositoriesModule, EnvironmentConfigModule],
  controllers: [
    AuthController,
    ResourceController,
    RoleController,
    DataRoleController,
    UserController,
    AccountController,
    FileController,
    FileImportHistoryController,
    ImportController,
    ApiLogController,
    MobileManagementController,
    HealthController,
  ],
  providers: [
    authUsecases,
    EmailUsecases,
    RedisUsecases,
    ResourceUsecases,
    RoleUsecases,
    RolePermissionUsecases,
    DataRoleUsecases,
    DataPermissionUsecases,
    ResourceActionUsecases,
    UserUsecases,
    UserVerifyUsecases,
    AccountUsecases,
    FileUsecases,
    FileImportHistoryUsecases,
    ImportUsecases,
    LoginLogUsecases,
    ApiLogUsecases,
    MobileManagementUsecases,
    SocketService,
    SocketGateway,
    ChannelsService,
  ],
})
export class ControllersModule {}
