import { ApiPropertyOptional, OmitType, PartialType } from '@nestjs/swagger';
import { IsBoolean, IsInt, IsOptional, IsString } from 'class-validator';
import { PaginationDto } from '../../../domain/dtos/pagination.dto';

export class GetListApiLogDto extends PartialType(
  OmitType(PaginationDto, ['searchString', 'isEmptyResult']),
) {
  @ApiPropertyOptional({
    type: String,
    description: 'Controler muốn tìm kiếm',
  })
  @IsOptional()
  @IsString()
  controller?: string;
  @ApiPropertyOptional({ type: String, description: 'Path muốn tìm kiếm' })
  @IsOptional()
  @IsString()
  route?: string;
  @ApiPropertyOptional({ type: String, description: 'Method muốn tìm kiếm' })
  @IsOptional()
  @IsString()
  method?: string;
  @ApiPropertyOptional({
    type: String,
    description: 'Http status muốn tìm kiếm',
  })
  @IsOptional()
  @IsInt()
  statusCode?: number;
  @ApiPropertyOptional({
    type: String,
    description: 'Trạng thái gọi api muốn tìm kiếm',
  })
  @IsOptional()
  @IsBoolean()
  isSuccess?: boolean;
}
