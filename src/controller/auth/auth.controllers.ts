import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Request,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { IAuthUserPayload } from '../../domain/interface/auth-user-payload.interface';
import { authUsecases } from '../../usecases/auth.usecases';
import { EUserPermission } from '../../utils/contants/permission.enum';
import { AzureADAuthGuard } from '../../utils/guard/azure-ad-auth.guard';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { NewAuthUser } from '../../utils/interceptors/new-user.decorator';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import {
  ChangePasswordByAdminDto,
  ChangePasswordDto,
} from './dtos/change-password.dto';
import { ForgotPasswordDto, LoginDto, RefreshTokenDto } from './dtos/login.dto';
import { ResendOtpDto, VerifyOtpDto } from './dtos/otp.dto';
import { ResetPasswordDto } from './dtos/reset-password.dto';

@Controller('/auth')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiTags('Auth')
export class AuthController {
  constructor(private readonly _authUsecases: authUsecases) {}

  @Post('/login')
  async login(@Body() data: LoginDto) {
    return await this._authUsecases.login(data, '');
  }

  @Post('/refresh')
  async refresh(@Body() refreshTokenDto: RefreshTokenDto): Promise<any> {
    const user = this._authUsecases.refresh(refreshTokenDto.refreshToken);
    return user;
  }

  @Get('/verify')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard)
  async verify(@NewAuthUser() user: any): Promise<any> {
    return { userInfo: user, time: `${Date.now()}` };
  }

  @Post('/verify-otp')
  async verifyOtp(@Body() body: VerifyOtpDto) {
    return this._authUsecases.verifyOtp(body);
  }

  @Post('/resend-otp')
  async resendOtp(@Body() body: ResendOtpDto) {
    return this._authUsecases.resendOtp(body, '');
  }

  @Post('/forgot-password')
  async forgotPassword(@Body() body: ForgotPasswordDto) {
    return this._authUsecases.forgotPassword(body, '');
  }

  @Post('/reset-password')
  async resetPassword(@Body() body: ResetPasswordDto) {
    await this._authUsecases.resetPassword(body);

    return { message: 'Successfully!' };
  }

  @Post('/change-password')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard)
  async changePassword(
    @Body() body: ChangePasswordDto,
    @NewAuthUser() jwtPayload: IAuthUserPayload,
  ) {
    await this._authUsecases.changePassword(body, jwtPayload);

    return { message: 'Successfully!' };
  }

  @Get('microsoft')
  @UseGuards(AzureADAuthGuard)
  async microsoftLogin(): Promise<any> {
    //return HttpStatus.OK;
  }

  @UseGuards(AzureADAuthGuard)
  @Get('/azure-ad')
  async azureVerify(@Request() request: any): Promise<any> {
    //console.log(user.headers);
    const token = this.extractTokenFromHeader(request);
    const acessInfo = this._authUsecases.loginByAzureAD(
      token,
      request.headers['x-platform'],
    );
    return acessInfo;
  }
  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }

  // @Get('microsoft/redirect')
  // @UseGuards(AzureADAuthGuard)
  // async microsoftLoginCallback(@Req() req) {
  //   // console.log(req);
  //   return this._authUsecases.loginByAzureAD(
  //     req.user,
  //     req.query.platform,
  //     req.query.portal,
  //   );
  // }

  @Get('/oauth/callback')
  async oauthCallback(@Query() req: any) {
    const { code } = req;

    return this._authUsecases.callbackAzureAd(code);
  }

  @Get('validate-token')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewAuthGuard)
  async validatePayload(@NewAuthUser() jwtPayload: any) {
    return { ...jwtPayload };
  }

  @Post('/change-password-by-admin')
  @ApiBearerAuth('Authorization')
  @UseGuards(NewPermissionGuard([EUserPermission.CREATE]))
  @UseGuards(NewAuthGuard)
  async changePasswordByAdmin(@Body() body: ChangePasswordByAdminDto) {
    await this._authUsecases.changePasswordByAdmin(body);

    return { message: 'Successfully!' };
  }
}
