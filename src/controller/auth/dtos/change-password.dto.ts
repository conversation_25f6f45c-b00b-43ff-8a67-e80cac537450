import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsStrongPassword } from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class ChangePasswordDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  readonly oldPassword: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsStrongPassword(
    {
      minLength: 8,
      minLowercase: 1,
      minNumbers: 1,
      minUppercase: 1,
      minSymbols: 1,
    },
    {
      message: 'VALIDATE.PASSWORD.IS_NOT_STRONG_ENOUGH',
    },
  )
  readonly newPassword: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  readonly confirmPassword: string;
}

export class ChangePasswordByAdminDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsStrongPassword(
    {
      minLength: 8,
      minLowercase: 1,
      minNumbers: 1,
      minUppercase: 1,
      minSymbols: 1,
    },
    {
      message: 'VALIDATE.PASSWORD.IS_NOT_STRONG_ENOUGH',
    },
  )
  readonly newPassword: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  platform: EPlatform;
}
