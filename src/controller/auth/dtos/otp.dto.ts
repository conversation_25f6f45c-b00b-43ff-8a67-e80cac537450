import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class VerifyOtpDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'VERIFY_TOKEN_IS_REQUIRE' })
  readonly verifyToken: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'OTP_IS_REQUIRE' })
  readonly otp: string;
}

export class ResendOtpDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'VERIFY_TOKEN_IS_REQUIRE' })
  readonly verifyToken: string;
}
