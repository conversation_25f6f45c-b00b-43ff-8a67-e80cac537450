import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsStrongPassword,
} from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';
import { EPortal } from '../../../domain/config/enums/portal.enum';

export class LoginDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'USERNAME_IS_REQUIRE' })
  readonly userName: string;

  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'PASSWORD_IS_REQUIRE' })
  readonly password: string;

  @ApiProperty({
    type: EPlatform,
    enum: EPlatform,
    required: false,
    description: 'Platform',
  })
  // @IsNotEmpty({ message: 'VALIDATE.PLATFORM.IS_REQUIRED' })
  @IsOptional()
  @IsEnum(EPlatform)
  readonly platform: EPlatform = EPlatform.E_PURCHASE;

  @ApiProperty({
    type: EPortal,
    enum: EPortal,
    required: false,
    description: 'Portal',
  })
  // @IsNotEmpty({ message: 'VALIDATE.PLATFORM.IS_REQUIRED' })
  @IsOptional()
  @IsEnum(EPortal)
  readonly portal: EPortal = EPortal.WEB_ADMIN;
}

export class RefreshTokenDto {
  @ApiProperty({ required: true })
  @IsNotEmpty({ message: 'REFRESH_TOKEN_IS_REQUIRE' })
  readonly refreshToken: string;
}

export class AddUserDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  readonly username: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @IsStrongPassword(
    {
      minLength: 8,
      minLowercase: 1,
      minNumbers: 1,
      minUppercase: 1,
      minSymbols: 1,
    },
    {
      message: 'VALIDATE.PASSWORD.IS_NOT_STRONG_ENOUGH',
    },
  )
  readonly password: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  readonly fullName: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  readonly email: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  readonly phone: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsBoolean()
  readonly isActive: boolean;

  @ApiPropertyOptional({ default: false })
  @IsOptional()
  @IsBoolean()
  readonly isAdmin: boolean;

  @ApiPropertyOptional({ default: false })
  @IsOptional()
  @IsBoolean()
  readonly isNeedOtp: boolean;
}

export class ForgotPasswordDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  readonly email: string;

  @ApiProperty({
    type: EPlatform,
    enum: EPlatform,
    required: false,
    description: 'Platform',
  })
  // @IsNotEmpty({ message: 'VALIDATE.PLATFORM.IS_REQUIRED' })
  @IsOptional()
  @IsEnum(EPlatform)
  readonly platform?: EPlatform;
}
