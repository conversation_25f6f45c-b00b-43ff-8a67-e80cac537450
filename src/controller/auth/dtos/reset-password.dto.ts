import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsStrongPassword } from 'class-validator';

export class ResetPasswordDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  readonly verifyToken: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsStrongPassword(
    {
      minLength: 8,
      minLowercase: 1,
      minNumbers: 1,
      minUppercase: 1,
      minSymbols: 1,
    },
    {
      message: 'VALIDATE.PASSWORD.IS_NOT_STRONG_ENOUGH',
    },
  )
  readonly password: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  readonly confirmPassword: string;
}
