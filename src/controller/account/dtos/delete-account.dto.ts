import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

export class DeleteAccount {
  @ApiProperty({ type: String, required: false, description: 'ID' })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.ID.MUST_BE_UUID' })
  id?: string;

  @ApiProperty({ type: String, required: false, description: 'Staff ID' })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.STAFF_ID.MUST_BE_UUID' })
  staffId?: string;

  @ApiProperty({ type: String, required: false, description: 'User ID' })
  @IsNotEmpty({ message: 'VALIDATE.USER_ID.IS_REQUIRED' })
  @IsUUID('4', { message: 'VALIDATE.USER_ID.MUST_BE_UUID' })
  userId: string;
}
