import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';
import { EPlatform } from '../../../domain/config/enums/platform.enum';

export class GetAccountDto {
  @ApiProperty({
    type: String,
    required: false,
    description: 'Email',
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Staff ID',
  })
  @IsOptional()
  // @IsString({ message: 'VALIDATE.STAFF_CODE.MUST_BE_STRING' })
  // @IsUUID('4', { message: 'VALIDATE.STAFF_ID.MUST_BE_UUID' })
  staffId?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Staff ID',
  })
  @IsOptional()
  @IsUUID('4', { message: 'VALIDATE.USER_ID.MUST_BE_UUID' })
  userId?: string;

  @ApiProperty({
    type: String,
    required: false,
    description: 'Staff Code',
  })
  @IsOptional()
  @IsString({ message: 'VALIDATE.STAFF_CODE.MUST_BE_STRING' })
  staffCode?: string;

  @ApiProperty({
    type: EPlatform,
    enum: EPlatform,
    required: false,
    description: 'Platform',
  })
  @IsOptional()
  @IsEnum(EPlatform)
  platform?: EPlatform;
}
