import {
  Body,
  Controller,
  Delete,
  Param,
  Patch,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AccountUsecases } from '../../usecases/account.usecase';
import { EStaffPermission } from '../../utils/contants/permission.enum';
import NewPermissionGuard from '../../utils/guard/new-permission.guard';
import { AspectLogger } from '../../utils/interceptors/logging.interceptor';
import { TransformationInterceptor } from '../../utils/interceptors/transform.interceptor';
import { NewAuthUser } from '../../utils/interceptors/new-user.decorator';
import { GetAccountDto } from './dtos/get-account.dto';
import { UpdateAccountDto } from './dtos/update-account.dto';
import { NewAuthGuard } from '../../utils/guard/new-auth.guard';

@Controller('/account')
@UseInterceptors(TransformationInterceptor)
@UseInterceptors(AspectLogger)
@ApiBearerAuth('Authorization')
@UseGuards(NewAuthGuard)
@ApiTags('Account')
export class AccountController {
  constructor(private readonly accountUsecases: AccountUsecases) {}

  @Patch(':staffId')
  @UseGuards(NewPermissionGuard([EStaffPermission.EDIT]))
  async update(
    @Param() param: GetAccountDto,
    @Body() data: UpdateAccountDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    return await this.accountUsecases.updateAccount(param.staffId, null, {
      ...data,
      updatedBy: {
        id: jwtPayload?.userId,
        firstName: jwtPayload?.firstName,
        lastName: jwtPayload?.lastName,
        email: jwtPayload?.email,
        phone: jwtPayload?.phone,
        staffId: jwtPayload?.staffId,
        staffCode: jwtPayload?.staffCode,
      },
    });
  }

  @Delete('staffId')
  @UseGuards(NewPermissionGuard([EStaffPermission.DELETE]))
  async deleteByStaffId(
    @Param() param: GetAccountDto,
    @NewAuthUser() jwtPayload: any,
  ) {
    await this.accountUsecases.deleteAccountByStaffId(
      param.staffId,
      jwtPayload,
    );
  }
}
