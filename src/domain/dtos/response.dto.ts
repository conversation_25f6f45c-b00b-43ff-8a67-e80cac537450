export class ResponseDto<T> {
  page: number;
  limit: number;
  total: number;
  results: T[];
  totalPages: number;
  nextPage: number;
  prevPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;

  constructor(data: T[], page: number, limit: number, total: number) {
    this.results = data;
    this.limit = limit;
    this.page = page;
    this.total = total;
    this.totalPages = Math.ceil(total / (limit || total));
    this.nextPage = this.totalPages > page ? page + 1 : null;
    this.prevPage = this.page > 1 ? page - 1 : null;
    this.hasNextPage = this.nextPage !== null;
    this.hasPrevPage = this.prevPage !== null;
  }
}
