export enum EventStatus {
  save = 4,
  treatment = 5,
  cancel = 6,
}

export enum EventDefine {
  treatment = 3,
  chooseGilt = 5,
  all = 0,
  vaccine = 2,
  HeatNotServed = 4,
  ServiceFailed = 7,
  Death = 9,
  Transfer = 10,
}

export enum ActionType {
  individual = 1,
  serials = 2,
}

export enum ChooseGiltType {
  choise = 1,
  reject = 2,
}

export enum herdStat {
  InpigSow = 7, // Nái mang thai
  Aborted = 6, // Sẩy thai
}

export const characterNumber = '**********';

export const fileImportPath = './uploads/imports';

export const regexPhone = /^[0-9]+$/;
