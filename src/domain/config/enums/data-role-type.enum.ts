export enum DataRoleType {
  SECTOR = 'SECTOR',
  COMPANY = 'COMPANY',
  BUSINESS_UNIT = 'BUSINESS_UNIT',
  DEPARTMENT = 'DEPARTMENT',
  FUNCTION_UNIT = 'FUNCTION_UNIT',
  BUSINESS_OWNER = 'BUSINESS_OWNER',
  MATERIAL = 'MATERIAL',
  MATERIAL_GROUP = 'MATERIAL_GROUP',
  MATERIAL_TYPE = 'MATERIAL_TYPE',
  PLANT = 'PLANT',
  SUPPLIER = 'SUPPLIER',
  PURCHASING_DEPARTMENT = 'PURCHASING_DEPARTMENT',
  PURCHASING_GROUP = 'PURCHASING_GROUP',
  POSITION = 'POSITION',
  CURRENCY_UNIT = 'CURRENCY_UNIT',
  PR_TYPE = 'PR_TYPE',
  PURCHASE_ORDER_TYPE = 'PURCHASE_ORDER_TYPE',
  SALE_ORG = 'SALE_ORG',
  DIVISION = 'DIVISION',
  DISTRIBUTION_CHANNEL = 'DISTRIBUTION_CHANNEL',
  PURCHASE_REQUEST = 'PURCHASE_REQUEST',
  PURCHASE_ORDER = 'PURCHASE_ORDER',
  SALE_GROUP = 'SALE_GROUP',
  COST = 'COST',
}
