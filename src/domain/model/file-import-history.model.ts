import { OmitType } from '@nestjs/swagger';
import { BaseModel } from './base.model';
import {
  EFileImportStatus,
  EFileImportType,
} from '../config/enums/file-import-history.enum';

export class FileImportHistoryModel extends OmitType(BaseModel, ['createdBy']) {
  fileName: string;
  createdBy: string | object;
  filePath: string;
  status: EFileImportStatus;
  importType: EFileImportType;
  errors?: string | object[];
  constructor(partial: Partial<FileImportHistoryModel>) {
    super();
    Object.assign(this, partial);
  }
}
