import { DataPermissionEntity } from '../../infrastructure/entities/data-permission.entity';
import { DataRoleEntity } from '../../infrastructure/entities/data-role.entity';
import { DataRoleType } from '../config/enums/data-role-type.enum';
import { EPlatform } from '../config/enums/platform.enum';
import { BaseModel } from './base.model';

export class DataRoleModel extends BaseModel {
  description: string;
  refCode: string;
  refId: string;
  searchValue: string;
  parentId: string;
  type: DataRoleType;
  isEnabled: boolean;
  platform: EPlatform;
  parent: DataRoleEntity | null;
  children: DataRoleEntity[];
  dataPermissions: DataPermissionEntity[];

  allowView?: boolean;
  constructor(partial: Partial<DataRoleModel>) {
    super();
    Object.assign(this, partial);
  }
}
