export class ApiLogModel {
  controller: string; // Tên controller
  method: string; // Method: GET, POST...
  route: string; // Endpoint API
  params?: any;
  query?: any;
  body?: any;
  statusCode: number; // HTTP Status Code
  isSuccess: boolean; // Thành công hay thất bại
  errorMessage?: any; // Mô tả lỗi (nếu có)
  duration?: number; // Thời gian thực thi API (ms)

  timestamp?: Date;
  constructor(partial: Partial<ApiLogModel>) {
    Object.assign(this, partial);
  }
}
