import { EStatus } from '../config/enums/status.enum';
import { AccountModel } from './account.model';
import { BaseModel } from './base.model';
import { RoleModel } from './role.model';

export class UserModel extends BaseModel {
  userName: string;
  password?: string;
  salt: string;
  passwordAlgorithm: string;
  status: EStatus;
  phone?: string = '';
  email?: string;
  isSuperAdmin: boolean;
  isMobile: boolean;
  isNeedOtp: boolean;
  code: string;
  roles: RoleModel[];
  accounts: AccountModel[];
  constructor(partial: Partial<UserModel>) {
    super();
    Object.assign(this, partial);
  }
}
