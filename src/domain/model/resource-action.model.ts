import { BaseModel } from './base.model';
import { ResourceModel } from './resource.model';
import { RolePermissionModel } from './role-permission.model';

export class ResourceActionModel extends BaseModel {
  actionAliases: string;
  description: string;
  resourceId: string;
  resourceIdGroup: string;
  searchValue: string;
  isEnabled: boolean;
  resource: ResourceModel | null;
  resourceGroup: ResourceModel | null;
  rolePermissions: RolePermissionModel[];

  constructor(partial: Partial<ResourceActionModel>) {
    super();
    Object.assign(this, partial);
  }
}
