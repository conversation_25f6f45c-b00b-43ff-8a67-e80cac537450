import { EStatus } from '../config/enums/status.enum';
import { BaseModel } from './base.model';

export class StaffModel extends BaseModel {
  firstName: string;
  lastName: string;
  code: string;
  phone: string;
  email: string;
  status: EStatus;

  managers?: StaffModel[];

  level?: number; //Level Management
  fullName?: string;

  constructor(partial: Partial<StaffModel>) {
    super();
    Object.assign(this, partial);
  }
}
