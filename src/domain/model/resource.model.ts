import { EPlatform } from '../config/enums/platform.enum';
import { EPortal } from '../config/enums/portal.enum';
import { BaseModel } from './base.model';
import { ResourceActionModel } from './resource-action.model';
import { RolePermissionModel } from './role-permission.model';

export class ResourceModel extends BaseModel {
  resourceAliases: string;
  description: string;
  parentId: string;
  searchValue: string;
  isEnabled: boolean;
  resourceOrder: number;
  platform: EPlatform;
  portal: EPortal;
  parent: ResourceModel | null;
  children: ResourceModel[];
  rolePermissions: RolePermissionModel[];
  resourceActions: ResourceActionModel[];
  resourceGroupActions: ResourceActionModel[];
  constructor(partial: Partial<ResourceModel>) {
    super();
    Object.assign(this, partial);
  }
}
