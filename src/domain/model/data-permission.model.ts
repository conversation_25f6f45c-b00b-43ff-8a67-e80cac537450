import { DataRoleEntity } from '../../infrastructure/entities/data-role.entity';
import { RoleEntity } from '../../infrastructure/entities/role.entity';
import { BaseModel } from './base.model';

export class DataPermissionModel extends BaseModel {
  roleId: string;
  isPersonal: boolean;
  dataRoleId: string;
  allowView: boolean;
  role: RoleEntity | null;
  dataRole: DataRoleEntity | null;
  constructor(partial: Partial<DataPermissionModel>) {
    super();
    Object.assign(this, partial);
  }
}
