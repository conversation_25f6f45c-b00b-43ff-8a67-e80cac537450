import { EPlatform } from '../config/enums/platform.enum';
import { EStatus } from '../config/enums/status.enum';
import { BaseModel } from './base.model';
import { UserModel } from './user.model';

export class AccountModel extends BaseModel {
  userName: string;
  firstName: string;
  lastName: string;
  email: string;
  platform: EPlatform;
  staffId: string;
  staffCode: string;
  status: EStatus;
  user: UserModel;
  isAdmin: boolean;
  constructor(partial: Partial<AccountModel>) {
    super();
    Object.assign(this, partial);
  }
}
