import { BaseModel } from './base.model';
import { ResourceActionModel } from './resource-action.model';
import { ResourceModel } from './resource.model';
import { RoleModel } from './role.model';

export class RolePermissionModel extends BaseModel {
  roleId: string;
  actionView: boolean;
  actionCreate: boolean;
  actionEdit: boolean;
  actionDelete: boolean;
  resourceId: string;
  resourceActionId: string;
  role: RoleModel | null;
  resource: ResourceModel | null;
  resourceAction: ResourceActionModel | null;
  constructor(partial: Partial<RolePermissionModel>) {
    super();
    Object.assign(this, partial);
  }
}
