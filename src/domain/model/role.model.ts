import { EPlatform } from '../config/enums/platform.enum';
import { EStatus } from '../config/enums/status.enum';
import { BaseModel } from './base.model';
import { DataRoleModel } from './data-role.model';
import { ResourceModel } from './resource.model';
import { RolePermissionModel } from './role-permission.model';
import { UserModel } from './user.model';

export class RoleModel extends BaseModel {
  name: string;
  description: string;
  searchValue: string;
  parentId: string;
  allowInherit: boolean;
  path: string;
  parent: RoleModel | null;
  children: RoleModel[];
  rolePermissions: RolePermissionModel[];
  users: UserModel[];
  status: EStatus;
  platform: EPlatform;
  code: string;
  //Foreign Key
  resources?: ResourceModel[];
  nlevel?: number;
  dataRoles?: DataRoleModel[];
  countAssignedUser?: number;
  //Data Role Classification
  sectors?: DataRoleModel[];
  companies?: DataRoleModel[];
  businessUnits?: DataRoleModel[];
  businessOwners?: DataRoleModel[];
  departments?: DataRoleModel[];
  functionUnits?: DataRoleModel[];
  //Resource Classification
  webAdminResources?: ResourceModel[];
  webInternalResources?: ResourceModel[];
  // digi-aqua
  farmId?: string;
  constructor(partial: Partial<RoleModel>) {
    super();
    Object.assign(this, partial);
  }
}
