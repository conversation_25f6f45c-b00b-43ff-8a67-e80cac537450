export type TErrorMessage = {
  message: string;
  errorCode: string;
  detail?: string;
};

export type TErrorMessageImport = {
  error: TErrorMessage;
  row: number;
};

export const buildErrorDetail = (
  errorCode: string,
  message: string = null,
  detail: any = null,
): TErrorMessage => {
  const e: TErrorMessage = {
    errorCode,
    message,
  };
  if (detail) e.detail = detail;
  return e;
};
