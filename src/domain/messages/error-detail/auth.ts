import { TErrorMessage, buildErrorDetail } from '../error-message';

export const authErrorDetails = {
  E_0101(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0101', 'ACCOUNT_NOT_AVAILABLE', detail);
    return e;
  },
  E_0102(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0102', 'USER_NAME_PASSWORD_INVALID', detail);
    return e;
  },
  E_0103(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0103', 'USER_IN_ACTIVE', detail);
    return e;
  },
  E_0104(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0104', 'TOKEN_IS_INCORRECT', detail);
    return e;
  },
  E_0105(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0105', 'Unable to send email', detail);
    return e;
  },
  E_0106(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0106', 'OTP_IS_INCORRECT', detail);
    return e;
  },

  E_0107(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0107', 'OTP_IS_EXPIRED', detail);
    return e;
  },
  E_0108(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0108', 'PASSWORD_NOT_MATCH', detail);
    return e;
  },

  E_0109(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '0109',
      'Not authorized on this portal.',
      detail,
    );
    return e;
  },
  E_0110(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '0110',
      'The old password is incorrect.',
      detail,
    );
    return e;
  },
  E_0111(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '0111',
      'The new password must not be the same as the old password.',
      detail,
    );
    return e;
  },
};
