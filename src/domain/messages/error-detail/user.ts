import { TErrorMessage, buildErrorDetail } from '../error-message';

export const userErrorDetails = {
  E_0001(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0001', 'Email already registered', detail);
    return e;
  },
  E_0002(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0002', 'UserName already registered', detail);
    return e;
  },
  E_0003(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('0003', 'User not found', detail);
    return e;
  },
  E_0004(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_0004', 'Name is required', detail);
    return e;
  },
  E_0005(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_0005', 'Phone is required', detail);
    return e;
  },
  E_0006(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_0006', 'Phone format is invalid', detail);
    return e;
  },
  E_0007(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_0007', 'Email is required', detail);
    return e;
  },
  E_0008(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_0008', 'Email format is invalid', detail);
    return e;
  },
  E_0009(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_0009', 'Email is duplicated', detail);
    return e;
  },
  E_0010(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_0010', 'User name is duplicated', detail);
    return e;
  },
  E_0011(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      'E_0011',
      'The account already exists on this platform.',
      detail,
    );
    return e;
  },
  E_0012(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_0012', 'Duplicate email in file', detail);
    return e;
  },
  E_0013(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('E_0013', 'Duplicate username in file', detail);
    return e;
  },
};
