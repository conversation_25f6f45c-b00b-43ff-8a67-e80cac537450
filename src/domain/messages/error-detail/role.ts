import { TErrorMessage, buildErrorDetail } from '../error-message';
export const roleErrorDetails = {
  E_1001(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('1001', 'Role not found', detail);
    return e;
  },
  E_1002(detail: string = null): TErrorMessage {
    const e = buildErrorDetail(
      '1002',
      'Cannot delete a role that has been assigned to a user.',
      detail,
    );
    return e;
  },
  E_1003(detail: string = null): TErrorMessage {
    const e = buildErrorDetail('1003', 'Role is inactive.', detail);
    return e;
  },
};
