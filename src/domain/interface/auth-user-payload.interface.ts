import { EPlatform } from '../config/enums/platform.enum';
import { EPortal } from '../config/enums/portal.enum';
import { ETokenType } from '../config/enums/token-type.enum';

export interface IAuthUserPayload {
  tokenType: ETokenType;
  userId: string;
  firstName: string;
  lastName: string;
  staffId: string;
  staffCode: string;
  email: string;
  phone: string;
  isSuperAdmin: boolean;
  isNeedOtp: boolean;
  platform: EPlatform;
  portal: EPortal;
  isAdmin: boolean;
  scopes?: any;
  // only aqua
  isOneFarm?: string;
}
