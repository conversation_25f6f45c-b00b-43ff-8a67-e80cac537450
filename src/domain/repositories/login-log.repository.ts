import { GetListLoginLogDto } from '../../controller/auth/dtos/get-list-login-log.dto';
import { LoginLogDto } from '../../controller/auth/dtos/login-log.dto';
import { ResponseDto } from '../dtos/response.dto';
import { LoginLogModel } from '../model/login-log.model';

export abstract class ILoginLogRepository {
  create: (data: LoginLogDto) => Promise<LoginLogModel>;
  getList: (
    conditions: GetListLoginLogDto,
  ) => Promise<ResponseDto<LoginLogModel>>;
}
