import { GetAccountDto } from '../../controller/account/dtos/get-account.dto';
import { EPlatform } from '../config/enums/platform.enum';
import { AccountModel } from '../model/account.model';

export abstract class IAccountRepository {
  createAccount: (data: AccountModel) => Promise<AccountModel>;
  getAccountsByStaffId: (conditions: GetAccountDto) => Promise<AccountModel[]>;
  updateAccount: (accountModel: AccountModel) => Promise<AccountModel>;
  deleteAccountByUserId: (
    userId: string,
    platform?: EPlatform,
  ) => Promise<void>;
  deleteAccountByStaffId: (staffId: string, userId: string) => Promise<void>;
  getAccountByUserName: (
    userName: string,
    platform?: EPlatform,
  ) => Promise<AccountModel>;
}
