import { GetResourceActionListDto } from '../../controller/resource/dtos/get-resource-action.dto';
import { ResponseDto } from '../dtos/response.dto';
import { ResourceActionModel } from '../model/resource-action.model';

export abstract class IResourceActionRepository {
  createResourceAction: (
    data: ResourceActionModel,
  ) => Promise<ResourceActionModel>;
  updateResourceAction: (
    data: ResourceActionModel,
  ) => Promise<ResourceActionModel>;
  deleteResourceAction: (id: string) => Promise<void>;
  getResourceActionById: (id: string) => Promise<ResourceActionModel>;
  getResourceActions: (
    conditions: GetResourceActionListDto,
  ) => Promise<ResponseDto<ResourceActionModel>>;
}
