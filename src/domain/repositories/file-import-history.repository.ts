import { GetListFileImportHistoryDto } from '../../controller/file-import-history/dtos/get-list-file-import-history.dto';
import { ResponseDto } from '../dtos/response.dto';
import { FileImportHistoryModel } from '../model/file-import-history.model';

export abstract class IFileImportHistoryRepository {
  createFileImportHistory: (
    data: FileImportHistoryModel,
  ) => Promise<FileImportHistoryModel>;
  getListFileImportHistory: (
    conditions: GetListFileImportHistoryDto,
  ) => Promise<ResponseDto<FileImportHistoryModel>>;
  deleteFileImportHistory: (id: string) => Promise<void>;
  updateFileImportHistory: (
    data: FileImportHistoryModel,
  ) => Promise<FileImportHistoryModel>;
  getFileImportHistoryById: (id: string) => Promise<FileImportHistoryModel>;
}
