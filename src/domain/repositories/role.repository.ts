import { GetRoleListDto } from '../../controller/role/dtos/get-role-list.dto';
import { UpdateRoleDto } from '../../controller/role/dtos/update-role.dto';
import { EPlatform } from '../config/enums/platform.enum';
import { ResponseDto } from '../dtos/response.dto';
import { RoleModel } from '../model/role.model';

export abstract class IRoleRepository {
  createFirstRole: (data: RoleModel) => Promise<RoleModel>;
  createRole: (data: RoleModel) => Promise<RoleModel>;
  updateRole: (id: string, data: UpdateRoleDto) => Promise<RoleModel>;
  deleteRole: (id: string) => Promise<void>;
  getRoleById: (id: string) => Promise<RoleModel>;
  getRoles: (conditions: GetRoleListDto) => Promise<ResponseDto<RoleModel>>;
  getRolesByUserId: (userId: string) => Promise<RoleModel[]>;
  countUsersByRoleId: (roleId: string) => Promise<number>;
  getRoleByCodes: (codes: string[], platform: string) => Promise<RoleModel[]>;
  getRolesForDDL: (farmId: string) => Promise<RoleModel[]>;
  getAll: (platform: string) => Promise<RoleModel[]>;
  getByUser: (userId: string, platform: EPlatform) => Promise<RoleModel[]>;
  getRoleAllData: () => Promise<RoleModel>;
}
