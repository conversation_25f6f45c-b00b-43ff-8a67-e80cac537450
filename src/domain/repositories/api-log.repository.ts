import { CreateApiLogDto } from '../../controller/api-log/dtos/create-api-log.dto';
import { GetListApiLogDto } from '../../controller/api-log/dtos/get-list-api-log.dto';
import { ResponseDto } from '../dtos/response.dto';
import { ApiLogModel } from '../model/api-log.model';

export abstract class IApiLogRepository {
  create: (data: CreateApiLogDto) => Promise<ApiLogModel>;
  getList: (conditions: GetListApiLogDto) => Promise<ResponseDto<ApiLogModel>>;
}
