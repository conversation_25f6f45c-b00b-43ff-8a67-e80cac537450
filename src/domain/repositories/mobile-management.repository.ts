import { CreateMobileManagementDto } from '../../controller/mobile-management/dtos/create-mobile-management.dto';
import { UpdateMobileManagementDto } from '../../controller/mobile-management/dtos/update-mobile-management.dto';
import { MobileManagementEntity } from '../../infrastructure/schemas/mobile-management.schema';
import { EPlatform } from '../config/enums/platform.enum';

export abstract class IMobileManagementRepository {
  getDetailMobileManagement: (
    platform: EPlatform,
  ) => Promise<MobileManagementEntity>;
  createMobileManagement: (
    data: CreateMobileManagementDto,
  ) => Promise<MobileManagementEntity>;
  updateMobileManagement: (
    id: string,
    data: UpdateMobileManagementDto,
  ) => Promise<MobileManagementEntity>;
}
