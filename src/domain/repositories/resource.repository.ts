import { GetResourceListDto } from '../../controller/resource/dtos/get-resource-list.dto';
import { EPlatform } from '../config/enums/platform.enum';
import { EPortal } from '../config/enums/portal.enum';
import { ResourceModel } from '../model/resource.model';

export abstract class IResourceRepository {
  createResource: (data: ResourceModel) => Promise<ResourceModel>;
  updateResource: (data: ResourceModel) => Promise<ResourceModel>;
  deleteResource: (id: string) => Promise<void>;
  getResourceById: (id: string) => Promise<ResourceModel>;
  getResources: (conditions: GetResourceListDto) => Promise<ResourceModel[]>;
  getMaxResourceOrder: (parentId: string) => Promise<number>;
  getResourceByRoleId: (
    roleId: string,
    platform: EPlatform,
    portal: EPortal,
  ) => Promise<any>;
}
