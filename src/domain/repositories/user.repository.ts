import { GetApproverByPositionDto } from '../../controller/user/dtos/get-approver-by-position.dto';
import { GetUserListDto } from '../../controller/user/dtos/get-user-list.dto';
import { EPlatform } from '../config/enums/platform.enum';
import { ResponseDto } from '../dtos/response.dto';
import { AccountModel } from '../model/account.model';
import { UserModel } from '../model/user.model';

export abstract class IUserRepository {
  getUserByUserName: (
    username: string,
    platform?: EPlatform,
  ) => Promise<UserModel>;
  getByIdAndPassword: (id: string, encodePass: string) => Promise<UserModel>;
  getUserInfo: (
    id: string,
    platform?: EPlatform,
    selectPassword?: boolean,
    getAllPlatform?: boolean,
  ) => Promise<UserModel>;
  createUser: (data: UserModel) => Promise<UserModel>;
  getUserByEmail: (
    email: string,
    id?: string,
    platform?: EPlatform,
  ) => Promise<UserModel>;
  updateUserPassword: (userId: string, password: string) => Promise<void>;
  getUsers: (
    conditions: GetUserListDto,
    platform?: EPlatform,
  ) => Promise<ResponseDto<UserModel>>;
  countAssignedUserToRole: (roleId: string) => Promise<number>;
  deleteUser: (userId: string) => Promise<void>;
  updateUser: (userModel: UserModel) => Promise<UserModel>;
  getUserByEmails: (emails: string[]) => Promise<UserModel[]>;
  getUserByUserNames: (names: string[]) => Promise<AccountModel[]>;
  checkDuplicateUsername: (username: string) => Promise<UserModel>;
  approverByPosition: (
    conditions: GetApproverByPositionDto,
  ) => Promise<AccountModel[]>;
  getUserInfoMobile: (
    id: string,
    platform?: EPlatform,
    selectPassword?: boolean,
    getAllPlatform?: boolean,
  ) => Promise<UserModel>;
}
