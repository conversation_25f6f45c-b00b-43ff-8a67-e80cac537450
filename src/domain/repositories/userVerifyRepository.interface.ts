import { UpdateUserVerifyDto } from '../../controller/auth/dtos/update-user-verify.dto';
import { UserVerifyModel } from '../model/user-verify.model';

export abstract class IUserVerifyRepository {
  getUserVerify: (
    userId: string,
    tokenType: string,
  ) => Promise<UserVerifyModel>;
  updateUserVerify: (
    userId: string,
    userVerify: UpdateUserVerifyDto,
  ) => Promise<void>;
  createUserVerify: (userVerify: UserVerifyModel) => Promise<UserVerifyModel>;

  deleteUserVerify: (userId: string, tokenType: string) => Promise<void>;
}
