import { GetDataRoleListDto } from '../../controller/data-role/dtos/get-data-role-list.dto';
import { UpdateDataRoleDto } from '../../controller/data-role/dtos/update-data-role.dto';
import { EPlatform } from '../config/enums/platform.enum';
import { ResponseDto } from '../dtos/response.dto';
import { DataRoleModel } from '../model/data-role.model';

export abstract class IDataRoleRepository {
  createDataRole: (data: DataRoleModel) => Promise<DataRoleModel>;
  updateDataRole: (
    dataRoleId: string,
    updateDataRoleDto: UpdateDataRoleDto,
  ) => Promise<DataRoleModel>;
  deleteDataRole: (refId: string) => Promise<void>;
  getDataRoleById: (id: string) => Promise<DataRoleModel>;
  getDataRoles: (
    conditions: GetDataRoleListDto,
  ) => Promise<ResponseDto<DataRoleModel>>;
  getDataRoleByRoleId: (roleId: string, platform?: EPlatform) => Promise<any>;
  getDataRoleByRefId: (refId: string) => Promise<DataRoleModel>;
}
