import { DataPermissionModel } from '../model/data-permission.model';

export abstract class IDataPermissionRepository {
  createDataPermissions: (
    model: DataPermissionModel[],
  ) => Promise<DataPermissionModel[]>;
  updateDataPermission: (
    data: DataPermissionModel,
  ) => Promise<DataPermissionModel>;
  deleteDataPermissionByRoleId: (roleId: string) => Promise<void>;
  getDataPermissionById: (id: string) => Promise<DataPermissionModel>;
  getDataPermissions: () => Promise<DataPermissionModel[]>;
}
