import { RolePermissionModel } from '../model/role-permission.model';

export abstract class IRolePermissionRepository {
  createRolePermissions: (
    model: RolePermissionModel[],
  ) => Promise<RolePermissionModel[]>;
  updateRolePermission: (
    data: RolePermissionModel,
  ) => Promise<RolePermissionModel>;
  deleteRolePermissionByRoleId: (roleId: string) => Promise<void>;
  getRolePermissionById: (id: string) => Promise<RolePermissionModel>;
  getRolePermissions: () => Promise<RolePermissionModel[]>;
}
