server {
	root /var/www/html;

	index index.html index.htm index.nginx-debian.html;
	server_name e-purchase.exceltech.vn; # managed by Certbot


	location / {
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:8082/;
	}

	listen [::]:443 ssl ipv6only=on; # managed by Certbot
	listen 443 ssl; # managed by Certbot
	ssl_certificate /etc/letsencrypt/live/e-purchase.exceltech.vn/fullchain.pem; # managed by Certbot
	ssl_certificate_key /etc/letsencrypt/live/e-purchase.exceltech.vn/privkey.pem; # managed by Certbot
	include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
	ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}

server {
    if ($host = e-purchase.exceltech.vn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


	listen 80 ;
	listen [::]:80 ;
    server_name e-purchase.exceltech.vn;
    return 404; # managed by Certbot
}

server {
	root /var/www/html;

	index index.html index.htm index.nginx-debian.html;
	server_name sale-portal.exceltech.vn; # managed by Certbot


	location / {
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:8085/;
	}

	listen 443 ssl; # managed by Certbot
	ssl_certificate /etc/letsencrypt/live/sale-portal.exceltech.vn/fullchain.pem; # managed by Certbot
	ssl_certificate_key /etc/letsencrypt/live/sale-portal.exceltech.vn/privkey.pem; # managed by Certbot
	include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
	ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}

server {
    if ($host = sale-portal.exceltech.vn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


	listen 80 ;
	listen [::]:80 ;
    server_name sale-portal.exceltech.vn;
    return 404; # managed by Certbot
}

server {
	root /var/www/html;

	index index.html index.htm index.nginx-debian.html;
	server_name customer-saleportal.exceltech.vn; # managed by Certbot


	location / {
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:8087/;
	}

	listen 443 ssl; # managed by Certbot
	ssl_certificate /etc/letsencrypt/live/customer-saleportal.exceltech.vn/fullchain.pem; # managed by Certbot
	ssl_certificate_key /etc/letsencrypt/live/customer-saleportal.exceltech.vn/privkey.pem; # managed by Certbot
	include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
	ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}

server {
    if ($host = customer-saleportal.exceltech.vn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


	listen 80 ;
	listen [::]:80 ;
    server_name customer-saleportal.exceltech.vn;
    return 404; # managed by Certbot
}

server {

	root /var/www/html;

	index index.html index.htm index.nginx-debian.html;
	server_name e-purchase-api.exceltech.vn; # managed by Certbot


	location / {
		try_files $uri $uri/ =404;
	}

	location = /auth/login {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8080/auth-service/auth/login;
	}
	location = /auth/refresh {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8080/auth-service/auth/refresh;
	}
	location = /auth/verify-otp {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8080/auth-service/auth/verify-otp;
	}
	location = /auth/resend-otp {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8080/auth-service/auth/resend-otp;
	}
	location = /auth/forgot-password {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8080/auth-service/auth/forgot-password;
	}
	location = /auth/change-password {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8080/auth-service/auth/change-password;
	}
	location = /auth/verify-token {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8080/auth-service/auth/verify;
	}

	location = /auth-service-doc/swagger {
		sub_filter_once off;	
		proxy_pass http://127.0.0.1:8080/auth-service-doc/swagger;
	}

	location ~* ^/auth-service-doc/swagger/.+\.(xml|js|jpg|png|css|html|otf|eot|svg|ttf)$ {
		proxy_pass         http://127.0.0.1:8080;
		proxy_http_version 1.1;
		proxy_set_header   Upgrade $http_upgrade;
		proxy_set_header   Connection keep-alive;
		proxy_set_header   Host $host;
		proxy_cache_bypass $http_upgrade;
		proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header   X-Forwarded-Proto $scheme;
		expires 90d;
	}

	location = /purchase-service-doc/swagger {
		sub_filter_once off;	
		proxy_pass http://127.0.0.1:8086/purchase-service-doc/swagger;
	}

	location ~* ^/purchase-service-doc/swagger/.+\.(xml|js|jpg|png|css|html|otf|eot|svg|ttf)$ {
		proxy_pass         http://127.0.0.1:8086;
		proxy_http_version 1.1;
		proxy_set_header   Upgrade $http_upgrade;
		proxy_set_header   Connection keep-alive;
		proxy_set_header   Host $host;
		proxy_cache_bypass $http_upgrade;
		proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header   X-Forwarded-Proto $scheme;
		expires 90d;
	}
	
	location = /auth {
		internal;
		if ($request_method = 'OPTIONS') {
			return 200;
		}
		proxy_pass http://127.0.0.1:8080/auth-service/auth/verify;
		proxy_pass_request_body off;
		proxy_set_header Content-Length "";
		#proxy_set_header X-Original-URI $request_uri;
		proxy_set_header CallType $calltype;
	}
	
	location /auth-service/ {
		set $calltype "MAJOR";
		auth_request /auth;
		auth_request_set $auth_status $upstream_status;
		error_page 401 =401 /auth;
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:8080/auth-service/;
		proxy_redirect off;
	}

	location /purchase-service/ {
		set $calltype "MAJOR1";
		auth_request /auth;
		auth_request_set $auth_status $upstream_status;
		error_page 401 =401 /auth;
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:8086/purchase-service/;
		proxy_redirect off;
	}

	location = /approve-engine-doc/swagger {
		sub_filter_once off;	
		proxy_pass http://127.0.0.1:8084/approve-engine-doc/swagger;
	}

	location ~* ^/approve-engine-doc/swagger/.+\.(xml|js|jpg|png|css|html|otf|eot|svg|ttf)$ {
		proxy_pass         http://127.0.0.1:8084;
		proxy_http_version 1.1;
		proxy_set_header   Upgrade $http_upgrade;
		proxy_set_header   Connection keep-alive;
		proxy_set_header   Host $host;
		proxy_cache_bypass $http_upgrade;
		proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header   X-Forwarded-Proto $scheme;
		expires 90d;
	}
	
	location /approve-engine/ {
		set $calltype "MAJOR1";
		auth_request /auth;
		auth_request_set $auth_status $upstream_status;
		error_page 401 =401 /auth;
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:8084/approve-engine/;
		proxy_redirect off;
	}

	location /file-service/ {
		set $calltype "MAJOR1";
		#auth_request /auth;
		#auth_request_set $auth_status $upstream_status;
		#error_page 401 =401 /auth;
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:8094/file-service/;
		proxy_redirect off;
	}

	location /queue-service/ {
		set $calltype "MAJOR1";
		#auth_request /auth;
		#auth_request_set $auth_status $upstream_status;
		#error_page 401 =401 /auth;
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:8093/queue-service/;
		proxy_redirect off;
	}

	listen [::]:443 ssl; # managed by Certbot
	listen 443 ssl; # managed by Certbot
	ssl_certificate /etc/letsencrypt/live/e-purchase-api.exceltech.vn/fullchain.pem; # managed by Certbot
	ssl_certificate_key /etc/letsencrypt/live/e-purchase-api.exceltech.vn/privkey.pem; # managed by Certbot
	include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
	ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = e-purchase-api.exceltech.vn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


	listen 80 ;
	listen [::]:80 ;
    server_name e-purchase-api.exceltech.vn;
    return 404; # managed by Certbot


}
server {
	root /var/www/html;

	index index.html index.htm index.nginx-debian.html;
	server_name sale-portal-api.exceltech.vn; # managed by Certbot
	add_header 'Access-Control-Allow-Origin' '*';
	add_header 'Access-Control-Allow-Credentials' 'true';
	add_header 'Access-Control-Allow-Headers' 'Authorization,Accept,Origin,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Content-Range,Range';
	add_header 'Access-Control-Allow-Methods' 'GET,POST,OPTIONS,PUT,DELETE,PATCH';




	location / {
		# First attempt to serve request as file, then
		# as directory, then fall back to displaying a 404.
		try_files $uri $uri/ =404;
	}

	location = /auth/login {
	    auth_request off;
	    proxy_pass http://127.0.0.1:3001/base-service/auth/login;
	}

	location = /base-service-doc/swagger/ {
		sub_filter_once off;	
		proxy_pass http://127.0.0.1:3001/base-service-doc/swagger/;
	}

	location ~* ^/base-service-doc/swagger/.+\.(xml|js|jpg|png|css|html|otf|eot|svg|ttf)$ {
		proxy_pass         http://127.0.0.1:3001;
		proxy_http_version 1.1;
		proxy_set_header   Upgrade $http_upgrade;
		proxy_set_header   Connection keep-alive;
		proxy_set_header   Host $host;
		proxy_cache_bypass $http_upgrade;
		proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header   X-Forwarded-Proto $scheme;
		expires 90d;
	}

	location = /auth {
		internal;     
		proxy_pass http://127.0.0.1:3001/base-service/auth/verify;
		proxy_pass_request_body off;
		proxy_set_header Content-Length "";
		proxy_set_header X-Original-URI $request_uri;
		proxy_set_header CallType $calltype;
	}

	location /base-service/ {
#		set $calltype "MAJOR";
#		auth_request /auth;
#		#auth_request_set $auth_foo $upstream_http_foo;
#		auth_request_set $auth_status $upstream_status;
#		error_page 401 =401 /auth;
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		#proxy_set_header X-Authentication-Id: $auth_foo;
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:3001/base-service/;
		proxy_redirect off;
	}

    listen [::]:443 ssl; # managed by Certbot
    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/sale-portal-api.exceltech.vn/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/sale-portal-api.exceltech.vn/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}

server {
    if ($host = sale-portal-api.exceltech.vn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


	listen 80 ;
	listen [::]:80 ;
    server_name sale-portal-api.exceltech.vn;
    return 404; # managed by Certbot
}


server {

	root /var/www/html;

	index index.html index.htm index.nginx-debian.html;
	server_name digi-aqua-api.exceltech.vn; # managed by Certbot


	location / {
		try_files $uri $uri/ =404;
	}

	location = /auth/login {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8091/auth-service/auth/login;
	}
	location = /auth/refresh {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8091/auth-service/auth/refresh;
	}
	location = /auth/verify-otp {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8091/auth-service/auth/verify-otp;
	}
	location = /auth/resend-otp {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8091/auth-service/auth/resend-otp;
	}
	location = /auth/forgot-password {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8091/auth-service/auth/forgot-password;
	}
	location = /auth/change-password {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8091/auth-service/auth/change-password;
	}
	location = /auth/verify-token {
	    auth_request off;
	    proxy_pass http://127.0.0.1:8091/auth-service/auth/verify;
	}

	location = /auth-service-doc/swagger {
		sub_filter_once off;	
		proxy_pass http://127.0.0.1:8091/auth-service-doc/swagger;
	}

	location ~* ^/auth-service-doc/swagger/.+\.(xml|js|jpg|png|css|html|otf|eot|svg|ttf)$ {
		proxy_pass         http://127.0.0.1:8091;
		proxy_http_version 1.1;
		proxy_set_header   Upgrade $http_upgrade;
		proxy_set_header   Connection keep-alive;
		proxy_set_header   Host $host;
		proxy_cache_bypass $http_upgrade;
		proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header   X-Forwarded-Proto $scheme;
		expires 90d;
	}
	
	location = /auth {
		internal;
		if ($request_method = 'OPTIONS') {
			return 200;
		}
		proxy_pass http://127.0.0.1:8091/auth-service/auth/verify;
		proxy_pass_request_body off;
		proxy_set_header Content-Length "";
		#proxy_set_header X-Original-URI $request_uri;
		proxy_set_header CallType $calltype;
	}
	
	location /auth-service/ {
		set $calltype "MAJOR";
		auth_request /auth;
		auth_request_set $auth_status $upstream_status;
		error_page 401 =401 /auth;
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:8091/auth-service/;
		proxy_redirect off;
	}

	listen [::]:443 ssl; # managed by Certbot
	listen 443 ssl; # managed by Certbot
	ssl_certificate /etc/letsencrypt/live/digi-aqua-api.exceltech.vn/fullchain.pem; # managed by Certbot
	ssl_certificate_key /etc/letsencrypt/live/digi-aqua-api.exceltech.vn/privkey.pem; # managed by Certbot
	include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
	ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = digi-aqua-api.exceltech.vn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


	listen 80 ;
	listen [::]:80 ;
    server_name digi-aqua-api.exceltech.vn;
    return 404; # managed by Certbot
}

server {
	root /var/www/html;

	index index.html index.htm index.nginx-debian.html;
	server_name digi-aqua.exceltech.vn; # managed by Certbot


	location / {
		proxy_http_version 1.1;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header HOST $host;
		proxy_set_header X-NginX-Proxy true;
		proxy_set_header Upgrade $http_upgrade;
		proxy_set_header Connection 'upgrade';
		proxy_cache_bypass $http_upgrade;
		proxy_pass http://127.0.0.1:8092/;
	}

	listen 443 ssl; # managed by Certbot
	ssl_certificate /etc/letsencrypt/live/digi-aqua.exceltech.vn/fullchain.pem; # managed by Certbot
	ssl_certificate_key /etc/letsencrypt/live/digi-aqua.exceltech.vn/privkey.pem; # managed by Certbot
	include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
	ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}

server {
    if ($host = digi-aqua.exceltech.vn) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


	listen 80 ;
	listen [::]:80 ;
    server_name digi-aqua.exceltech.vn;
    return 404; # managed by Certbot
}