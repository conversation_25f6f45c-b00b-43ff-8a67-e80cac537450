ENV=
#MONGO
MONGO_DB_URI=
#POSTGRES
DATABASE_HOST_SLAVE_1=
DATABASE_HOST=
DATABASE_PORT=
DATABASE_USER=
DATABASE_PASSWORD=
DATABASE_NAME=
DATABASE_SCHEMA=
DATABASE_SYNCHRONIZE=
 
#JWT
JWT_SECRET=
JWT_EXPIRATION_TIME=
JWT_REFRESH_TOKEN_SECRET=
JWT_REFRESH_TOKEN_EXPIRATION_TIME=

 
#REDIS
REDIS_HOST=
REDIS_PORT=
REDIS_TLS=
REDIS_PASSWORD=
 
#NODE MAILER
MAIL_SMTP_HOST=
MAIL_SMTP_PORT=
MAIL_SMTP_USER=
MAIL_SMTP_PASS=
MAIL_SMTP_SECURE=
MAIL_CC=
 
#TOKEN VERIFY
OTP_MAX_TIME_SENT=
OTP_MAX_TIME_STAMP_SENT=
OTP_EXPIRE_TIME=
OTP_TOKEN_EXPIRE_TIME=

#BASIC AUTH
BASIC_USER=
BASIC_PASS=

IDENTITY_SERVICE_API_URL=

IDENTITY_LOGIN_API_URL=

IDENTITY_QUEUE_SERVICE_API_URL=

IDENTITY_FILE_SERVICE_API_URL=

IDENTITY_PURCHASE_SERVICE_API_URL=

IDENTITY_AQUA_SERVICE_API_URL=
#AZURE AD
AZURE_AD_CLIENT_ID=
AZURE_AD_TENANT_ID=
AZURE_AD_CLIENT_SECRET=
AZURE_AD_REFRESH_TOKEN=
AZURE_AD_ACCESS_URL=

API_KEY=
